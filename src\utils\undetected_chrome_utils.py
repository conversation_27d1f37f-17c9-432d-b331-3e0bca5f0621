#!/usr/bin/env python3
"""
Undetected ChromeDriver工具模块
专门用于绕过反爬虫检测的Chrome浏览器设置
"""

import logging
import random
import time
from pathlib import Path
from typing import Optional

try:
    import undetected_chromedriver as uc
    UNDETECTED_AVAILABLE = True
except ImportError:
    UNDETECTED_AVAILABLE = False
    import warnings
    warnings.warn("undetected-chromedriver未安装，请运行: pip install undetected-chromedriver")

logger = logging.getLogger(__name__)

class UndetectedChromeManager:
    """Undetected ChromeDriver管理器"""
    
    def __init__(self):
        self.driver = None
        self.options = None
        
    def create_options(self, headless: bool = False, profile_path: str = None) -> 'uc.ChromeOptions':
        """创建优化的Chrome选项"""
        if not UNDETECTED_AVAILABLE:
            raise ImportError("undetected-chromedriver未安装")
            
        options = uc.ChromeOptions()
        
        # 基础设置
        if headless:
            options.add_argument('--headless=new')  # 使用新的headless模式
            
        # 窗口和显示设置
        options.add_argument('--start-maximized')
        options.add_argument('--window-size=1920,1080')
        
        # 性能优化
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-extensions')
        
        # 反检测核心设置（undetected-chromedriver会自动处理大部分）
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-features=VizDisplayCompositor')
        
        # LinkedIn特定优化
        options.add_argument('--disable-web-security')
        options.add_argument('--allow-running-insecure-content')
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--ignore-ssl-errors')
        
        # 用户体验优化
        options.add_argument('--disable-notifications')
        options.add_argument('--disable-popup-blocking')
        options.add_argument('--disable-translate')
        options.add_argument('--disable-background-timer-throttling')
        
        # 内存和缓存优化
        options.add_argument('--memory-pressure-off')
        options.add_argument('--max_old_space_size=4096')
        
        # 设置用户数据目录（如果指定）
        if profile_path:
            profile_path = Path(profile_path)
            profile_path.mkdir(parents=True, exist_ok=True)
            options.add_argument(f'--user-data-dir={profile_path}')
            logger.info(f"使用用户配置目录: {profile_path}")
        
        # 随机用户代理（增强反检测）
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36'
        ]
        
        selected_ua = random.choice(user_agents)
        options.add_argument(f'--user-agent={selected_ua}')
        logger.debug(f"使用用户代理: {selected_ua}")
        
        # 实验性选项
        prefs = {
            "profile.default_content_setting_values": {
                "notifications": 2,  # 禁用通知
                "geolocation": 2,    # 禁用地理位置
                "media_stream": 2,   # 禁用媒体流
            },
            "profile.managed_default_content_settings": {
                "images": 1  # 允许图片加载
            }
        }
        options.add_experimental_option("prefs", prefs)
        
        # 排除自动化开关（undetected-chromedriver会自动处理这些）
        # options.add_experimental_option("excludeSwitches", ["enable-automation"])
        # options.add_experimental_option('useAutomationExtension', False)
        
        self.options = options
        return options
    
    def create_driver(self,
                     headless: bool = False,
                     profile_path: str = None,
                     version_main: int = None,
                     driver_executable_path: str = None) -> 'uc.Chrome':
        """创建Undetected Chrome驱动"""
        if not UNDETECTED_AVAILABLE:
            raise ImportError("undetected-chromedriver未安装")

        # 尝试不同的Chrome版本以解决兼容性问题
        chrome_versions = []
        if version_main:
            chrome_versions.append(version_main)
        chrome_versions.extend([137, 136, 135, None])  # 常见版本 + 自动检测

        last_error = None

        for version in chrome_versions:
            try:
                logger.info(f"尝试创建Undetected ChromeDriver (Chrome版本: {version if version else '自动检测'})...")

                # 创建选项
                options = self.create_options(headless=headless, profile_path=profile_path)

                # 创建驱动参数
                driver_kwargs = {
                    'options': options,
                    'use_subprocess': True,  # 推荐设置
                    'keep_alive': True,      # 保持连接
                }

                # 指定Chrome版本
                if version:
                    driver_kwargs['version_main'] = version
                    logger.info(f"指定Chrome版本: {version}")

                # 指定驱动路径（如果需要）
                if driver_executable_path:
                    driver_kwargs['driver_executable_path'] = driver_executable_path
                    logger.info(f"使用指定驱动路径: {driver_executable_path}")

                # 创建驱动
                self.driver = uc.Chrome(**driver_kwargs)

                # 执行反检测脚本
                self._apply_stealth_scripts()

                # 设置超时
                self.driver.implicitly_wait(10)
                self.driver.set_page_load_timeout(30)

                logger.info(f"Undetected ChromeDriver创建成功 (Chrome版本: {version if version else '自动'})")
                return self.driver

            except Exception as e:
                last_error = e
                logger.warning(f"Chrome版本 {version if version else '自动'} 创建失败: {str(e)}")

                # 清理失败的驱动
                if hasattr(self, 'driver') and self.driver:
                    try:
                        self.driver.quit()
                    except:
                        pass
                    self.driver = None

                continue

        # 所有版本都失败
        logger.error(f"所有Chrome版本都创建失败，最后错误: {str(last_error)}")
        raise RuntimeError(f"创建Undetected ChromeDriver失败: {str(last_error)}")
    
    def _apply_stealth_scripts(self):
        """应用额外的反检测脚本"""
        if not self.driver:
            return
            
        try:
            # 移除webdriver属性
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)
            
            # 修改plugins长度
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
            """)
            
            # 修改语言设置
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });
            """)
            
            # 添加Chrome对象
            self.driver.execute_script("""
                window.chrome = {
                    runtime: {},
                };
            """)
            
            # 修改权限查询
            self.driver.execute_script("""
                const originalQuery = window.navigator.permissions.query;
                return window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
            """)
            
            logger.debug("反检测脚本应用成功")
            
        except Exception as e:
            logger.warning(f"应用反检测脚本时出错: {str(e)}")
    
    def random_delay(self, min_seconds: float = 1.0, max_seconds: float = 3.0):
        """随机延迟，模拟人类行为"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
        logger.debug(f"随机延迟: {delay:.2f}秒")
    
    def human_like_scroll(self, element=None):
        """人类化滚动"""
        if not self.driver:
            return
            
        try:
            if element:
                # 滚动到特定元素
                self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth'});", element)
            else:
                # 随机滚动
                scroll_height = self.driver.execute_script("return document.body.scrollHeight")
                current_position = self.driver.execute_script("return window.pageYOffset")
                
                # 随机滚动距离
                scroll_distance = random.randint(200, 800)
                new_position = min(current_position + scroll_distance, scroll_height)
                
                self.driver.execute_script(f"window.scrollTo({{top: {new_position}, behavior: 'smooth'}});")
                
            self.random_delay(0.5, 1.5)
            
        except Exception as e:
            logger.warning(f"人类化滚动时出错: {str(e)}")
    
    def quit(self):
        """安全退出驱动"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("Undetected ChromeDriver已退出")
            except Exception as e:
                logger.warning(f"退出驱动时出错: {str(e)}")
            finally:
                self.driver = None

# 便捷函数
def create_undetected_chrome(headless: bool = False, 
                           profile_path: str = None,
                           version_main: int = None) -> 'uc.Chrome':
    """创建Undetected Chrome驱动的便捷函数"""
    manager = UndetectedChromeManager()
    return manager.create_driver(
        headless=headless,
        profile_path=profile_path,
        version_main=version_main
    )

def check_undetected_availability() -> bool:
    """检查undetected-chromedriver是否可用"""
    return UNDETECTED_AVAILABLE

def install_undetected_chromedriver():
    """安装undetected-chromedriver的提示"""
    if not UNDETECTED_AVAILABLE:
        print("请安装undetected-chromedriver:")
        print("pip install undetected-chromedriver")
        return False
    return True
