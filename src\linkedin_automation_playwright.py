"""LinkedIn自动化模块 - Playwright版本
实现LinkedIn登录验证、搜索职位、筛选Easy Apply职位并进行投递的功能
使用Playwright替代Selenium实现更稳定的浏览器自动化
"""

import time
import random
from typing import List, Dict, Optional, Tuple, Any, Union
import yaml
from pathlib import Path
from loguru import logger
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, Page, BrowserContext, Playwright, TimeoutError as PlaywrightTimeoutError

class LinkedInAutomationPlaywright:
    """LinkedIn自动化类 - Playwright版本"""
    
    # --- 反爬虫增强 ---
    _UA_POOL = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0"
    ]
    _PROXY_POOL = []  # 可在配置文件中扩展
    _last_used_ua = None
    _last_used_proxy = None

    def __init__(self, config_path_or_dict = None):
        """
        初始化LinkedIn自动化实例

        Args:
            config_path_or_dict: 配置文件路径或配置字典
        """
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        self.config = self._load_config(config_path_or_dict)
        self.is_logged_in = False
        self.applied_jobs = set()  # 记录已申请的职位
        
    def _load_config(self, config_path_or_dict) -> Dict:
        """加载配置文件或配置字典"""
        # 如果传入的是字典，直接返回
        if isinstance(config_path_or_dict, dict):
            return config_path_or_dict

        # 如果传入的是文件路径，尝试加载
        if config_path_or_dict and Path(config_path_or_dict).exists():
            with open(config_path_or_dict, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        
        # 默认配置
        return {
            'linkedin': {
                'email': '',
                'password': '',
                'search_keywords': ['python developer', 'software engineer'],
                'location': 'United States',
                'experience_level': ['Entry level', 'Associate', 'Mid-Senior level'],
                'job_type': ['Full-time', 'Part-time', 'Contract'],
                'remote_work': ['On-site', 'Remote', 'Hybrid'],
                'max_applications_per_day': 50,
                'delay_between_applications': [30, 60],  # 秒
                'auto_answer_questions': True,
                'default_answers': {
                    'years_experience': '3',
                    'willing_to_relocate': 'Yes',
                    'authorized_to_work': 'Yes',
                    'require_sponsorship': 'No'
                }
            },
            'playwright': {
                'headless': False,
                'timeout': 30000,  # 毫秒
                'window_size': [1200, 800]
            }
        }
    
    def _random_user_agent(self):
        return random.choice(self._UA_POOL)

    def _random_proxy(self):
        if self._PROXY_POOL:
            return random.choice(self._PROXY_POOL)
        return None

    def setup_driver(self, headless: bool = None, user_agent: str = None, proxy: str = None, force_restart: bool = False) -> Page:
        """设置Playwright浏览器，支持动态UA/代理

        Args:
            headless: 是否使用无头模式，如果为None则使用配置文件中的设置
            user_agent: 自定义用户代理，如果为None则随机选择一个
            proxy: 自定义代理，如果为None则不使用代理
            force_restart: 是否强制重启浏览器

        Returns:
            Playwright Page对象

        Raises:
            RuntimeError: 设置浏览器失败时抛出
        """
        try:
            # 检查是否需要重新启动浏览器
            if not force_restart and self.page and self.browser and self.context:
                try:
                    # 测试浏览器是否仍然可用
                    self.page.url
                    logger.debug("浏览器实例仍然可用，跳过重新初始化")
                    return self.page
                except Exception:
                    logger.debug("浏览器实例不可用，需要重新初始化")

            # 只有在强制重启或浏览器不可用时才关闭现有实例
            if force_restart or not self.page:
                self.close()
            
            # 确定是否使用无头模式
            if headless is None:
                headless = self.config['playwright']['headless']
            
            # 随机选择UA和代理
            if not user_agent:
                user_agent = self._random_user_agent()
            if not proxy:
                proxy = self._random_proxy()
            
            self._last_used_ua = user_agent
            self._last_used_proxy = proxy
            
            logger.info(f"设置Playwright浏览器，无头模式: {headless}，UA: {user_agent}，代理: {proxy}")
            
            # 启动Playwright（处理asyncio循环冲突）
            try:
                self.playwright = sync_playwright().start()
            except RuntimeError as e:
                if "asyncio" in str(e).lower():
                    logger.warning("检测到asyncio循环冲突，尝试使用nest_asyncio解决...")
                    try:
                        import nest_asyncio
                        nest_asyncio.apply()
                        self.playwright = sync_playwright().start()
                    except ImportError:
                        logger.error("需要安装nest_asyncio: pip install nest_asyncio")
                        raise
                else:
                    raise
            
            # 启动浏览器（修正参数）
            launch_args = {}
            if proxy:
                launch_args['proxy'] = { 'server': proxy }
            self.browser = self.playwright.chromium.launch(
                headless=headless, **launch_args
            )
            
            # 窗口大小
            window_size = self.config['playwright']['window_size']
            
            # 创建上下文（在这里设置ignore_https_errors）
            context_args = {
                'viewport': {'width': window_size[0], 'height': window_size[1]},
                'user_agent': user_agent,
                'ignore_https_errors': True
            }
            self.context = self.browser.new_context(**context_args)
            
            # 添加反检测脚本
            self.context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
            """)
            
            # 创建页面
            self.page = self.context.new_page()
            
            # 设置超时
            self.page.set_default_timeout(self.config['playwright']['timeout'])
            
            logger.info("Playwright浏览器设置完成")
            return self.page
        
        except Exception as e:
            logger.error(f"设置Playwright浏览器失败: {str(e)}")
            raise RuntimeError(f"设置Playwright浏览器失败: {str(e)}")
    
    def login(self, email: str = None, password: str = None) -> Dict:
        """登录LinkedIn

        Args:
            email: LinkedIn账号邮箱，如果为None则使用配置文件中的设置
            password: LinkedIn账号密码，如果为None则使用配置文件中的设置

        Returns:
            包含登录结果的字典
        """
        try:
            if not email:
                email = self.config['linkedin']['email']
            if not password:
                password = self.config['linkedin']['password']

            if not email or not password:
                logger.error("LinkedIn邮箱或密码未配置")
                return {"success": False, "status": "邮箱或密码未配置", "requires_action": False}

            logger.info(f"开始登录LinkedIn... 使用邮箱: {email[:3]}****{email[-10:]}")

            # 确保浏览器已初始化
            if not self.page:
                logger.debug("浏览器未初始化，正在设置...")
                self.setup_driver()

            # 访问LinkedIn登录页面
            try:
                logger.debug("正在访问LinkedIn登录页面...")
                self.page.goto("https://www.linkedin.com/login", wait_until="networkidle")
                logger.debug(f"当前URL: {self.page.url}")
            except Exception as e:
                logger.error(f"无法访问LinkedIn登录页面: {str(e)}")
                return {"success": False, "status": "无法访问LinkedIn登录页面，请检查网络连接或代理设置", "requires_action": False}
            
            # 等待登录页面加载
            try:
                logger.debug("等待登录页面元素加载...")
                self.page.wait_for_selector("#username", state="visible", timeout=15000)
                logger.debug("登录页面元素加载成功")
            except Exception as e:
                logger.error(f"登录页面加载超时或异常: {str(e)}")
                return {"success": False, "status": "登录页面加载超时，请检查网络连接速度", "requires_action": False}
            
            # 输入邮箱
            try:
                logger.debug("正在输入邮箱...")
                self.page.click("#username")
                self.page.fill("#username", email)
                logger.info("邮箱输入完成")
            except Exception as e:
                logger.error(f"邮箱输入失败: {str(e)}")
                return {"success": False, "status": "邮箱输入失败，请检查邮箱格式是否正确", "requires_action": False}
            
            # 输入密码
            try:
                logger.debug("正在输入密码...")
                self.page.wait_for_selector("#password", state="visible", timeout=5000)
                self.page.focus("#password")  # 确保聚焦
                self.page.fill("#password", password)  # fill会自动清空再输入
                logger.info("密码输入完成")
            except Exception as e:
                logger.error(f"密码输入失败: {str(e)}")
                return {"success": False, "status": "密码输入失败，请检查密码格式是否正确", "requires_action": False}
            
            # 点击登录按钮
            try:
                logger.debug("正在点击登录按钮...")
                self.page.click("button[type='submit']")
                logger.info("点击登录按钮完成")
            except Exception as e:
                logger.error(f"登录按钮点击失败: {str(e)}")
                return {"success": False, "status": "登录按钮点击失败，请稍后重试", "requires_action": False}
            
            # 等待登录完成，增加等待时间以确保页面加载
            logger.debug("等待登录处理...")
            time.sleep(8)
            
            # 检查是否需要验证码或其他验证
            if "challenge" in self.page.url or "checkpoint/challenge" in self.page.url:
                logger.warning("检测到验证码验证")
                return {"success": False, "status": "需要完成验证码验证，请在浏览器中完成验证后重试", "requires_action": True}
            
            # 检查是否需要二次验证（checkpoint）
            if "checkpoint" in self.page.url:
                logger.warning("检测到二次验证，等待用户完成验证...")
                max_wait = 180  # 增加最长等待时间到180秒（3分钟）
                poll_interval = 0.5  # 减少检查间隔到0.5秒，更频繁检测
                waited = 0
                while waited < max_wait:
                    try:
                        current_url = self.page.url
                        logger.debug(f"二次验证等待中，当前URL: {current_url}")
                        if ("feed" in current_url or "linkedin.com/feed" in current_url or
                            self.page.query_selector(".global-nav") or self.page.query_selector(".feed-identity-module")):
                            logger.info("二次验证后检测到已登录，进入首页")
                            self.is_logged_in = True
                            return {"success": True, "status": "登录成功", "requires_action": False}
                    except Exception as e:
                        logger.warning(f"等待二次验证时页面异常: {str(e)}")
                    time.sleep(poll_interval)
                    waited += poll_interval
                logger.error("二次验证超时，未检测到登录成功")
                return {"success": False, "status": "二次验证超时，未检测到登录成功", "requires_action": False}
            
            # 验证登录状态（增强版循环检测）
            try:
                logger.debug("验证登录状态...（增强版循环检测）")
                max_wait = 120  # 增加等待时间到120秒（2分钟）
                poll_interval = 0.5  # 减少检测间隔到0.5秒，更频繁检测
                waited = 0

                while waited < max_wait:
                    try:
                        url = self.page.url
                        logger.debug(f"等待检测登录，当前URL: {url}")

                        # 更全面的登录状态检测
                        login_indicators = [
                            # URL检测
                            "feed" in url,
                            "jobs" in url,
                            "/in/" in url,  # 个人资料页面
                            "linkedin.com/feed" in url,

                            # 页面元素检测
                            self.page.query_selector(".global-nav"),
                            self.page.query_selector(".feed-identity-module"),
                            self.page.query_selector("[data-control-name='nav.settings_and_privacy']"),
                            self.page.query_selector(".nav-item__profile-member-photo"),
                            self.page.query_selector(".global-nav__me"),
                            self.page.query_selector(".global-nav__primary-link--me"),

                            # 检测是否有"我"的菜单
                            self.page.query_selector("button[aria-label*='我']"),
                            self.page.query_selector("button[aria-label*='Me']"),

                            # 检测LinkedIn主导航
                            self.page.query_selector(".global-nav__nav"),
                            self.page.query_selector(".global-nav__primary-items"),
                        ]

                        # 如果任何一个指标为真，则认为已登录
                        if any(login_indicators):
                            logger.info("检测到已登录，进入主页")
                            self.is_logged_in = True
                            return {"success": True, "status": "登录成功", "requires_action": False}

                        # 检测是否仍在登录页面
                        if "login" in url or "challenge" in url:
                            logger.debug("仍在登录或验证页面，继续等待...")
                        else:
                            # 如果不在登录页面但也没有检测到登录指标，可能需要更多时间
                            logger.debug("页面已跳转但未检测到登录指标，继续等待...")

                    except Exception as e:
                        logger.warning(f"检测主页元素时页面异常: {str(e)}")

                    time.sleep(poll_interval)
                    waited += poll_interval

                # 最后一次尝试：检查当前页面内容
                try:
                    current_url = self.page.url
                    page_content = self.page.content()

                    logger.debug(f"最终检测 - 当前URL: {current_url}")

                    # 如果URL包含这些关键词，很可能已经登录
                    if any(keyword in current_url for keyword in ["feed", "jobs", "/in/", "mynetwork"]):
                        logger.info("基于URL判断已登录成功")
                        self.is_logged_in = True
                        return {"success": True, "status": "登录成功", "requires_action": False}

                    # 检查页面内容中是否包含登录后的关键元素
                    if any(keyword in page_content for keyword in ["global-nav", "feed-identity", "nav-item__profile"]):
                        logger.info("基于页面内容判断已登录成功")
                        self.is_logged_in = True
                        return {"success": True, "status": "登录成功", "requires_action": False}

                except Exception as e:
                    logger.warning(f"最终检测时出错: {str(e)}")

                logger.error("未检测到登录成功")
                logger.debug("当前页面HTML片段: " + self.page.content()[:1000])
                return {"success": False, "status": "登录验证超时，请手动检查浏览器中的登录状态", "requires_action": True}

            except Exception as e:
                logger.error(f"登录验证失败: {str(e)}")
                return {"success": False, "status": "登录验证失败，请稍后重试", "requires_action": False}
                
        except Exception as e:
            logger.error(f"登录过程发生未知错误: {str(e)}")
            return {"success": False, "status": "登录过程发生未知错误，请稍后重试", "requires_action": False}

    def verify_login_status(self) -> Dict:
        """手动验证登录状态

        Returns:
            包含验证结果的字典
        """
        try:
            logger.info("手动验证登录状态...")

            current_url = self.page.url
            logger.info(f"当前页面URL: {current_url}")

            # 检查URL（扩展更多指标）
            url_indicators = ["feed", "jobs", "/in/", "mynetwork", "messaging", "notifications", "search/results"]
            url_logged_in = any(indicator in current_url for indicator in url_indicators)

            # 检查页面元素（扩展更多选择器）
            element_selectors = [
                ".global-nav",
                ".feed-identity-module",
                "[data-control-name='nav.settings_and_privacy']",
                ".nav-item__profile-member-photo",
                ".global-nav__me",
                ".global-nav__primary-link--me",
                "button[aria-label*='我']",
                "button[aria-label*='Me']",
                ".global-nav__nav",
                ".global-nav__primary-items",
                "[data-control-name='identity_welcome_message']",
                ".feed-identity",
                ".nav-item--me",
                "[data-test-global-nav-me]"
            ]

            elements_found = []
            for selector in element_selectors:
                try:
                    element = self.page.query_selector(selector)
                    if element:
                        elements_found.append(selector)
                except Exception:
                    continue

            logger.info(f"URL指标: {url_logged_in}")
            logger.info(f"找到的元素: {elements_found}")

            # 判断登录状态
            if url_logged_in or len(elements_found) > 0:
                self.is_logged_in = True
                logger.info("✅ 验证确认：已成功登录LinkedIn")
                return {"success": True, "status": "已确认登录成功", "requires_action": False}
            else:
                # 额外检查：页面内容检测
                try:
                    page_content = self.page.content()
                    content_indicators = ["global-nav", "feed-identity", "nav-item__profile", "linkedin.com/feed"]
                    content_logged_in = any(indicator in page_content for indicator in content_indicators)
                    
                    if content_logged_in:
                        self.is_logged_in = True
                        logger.info("✅ 基于页面内容验证确认：已成功登录LinkedIn")
                        return {"success": True, "status": "已确认登录成功", "requires_action": False}
                except Exception as e:
                    logger.warning(f"页面内容检测时出错: {str(e)}")
                
                logger.warning("❌ 验证失败：未检测到登录状态")
                return {"success": False, "status": "未检测到登录状态，请检查浏览器", "requires_action": True}

        except Exception as e:
            logger.error(f"验证登录状态时出错: {str(e)}")
            return {"success": False, "status": f"验证过程出错: {str(e)}", "requires_action": False}
    
    def is_linkedin_blocked(self, page_content: str) -> bool:
        """检测页面内容是否被LinkedIn风控（如验证码、robot、reCAPTCHA等）"""
        content_lower = page_content.lower()

        # 检查明确的风控关键词
        block_keywords = [
            "captcha", "recaptcha", "security check", "detected unusual traffic",
            "robot", "automation", "suspicious activity", "verify you're human",
            "please complete this security check", "unusual activity"
        ]

        # 如果包含风控关键词，进一步验证
        has_block_keywords = any(keyword in content_lower for keyword in block_keywords)

        if has_block_keywords:
            # 检查是否有正常的LinkedIn内容，如果有，可能是误判
            normal_keywords = [
                "jobs", "search results", "base-search-card", "job-card",
                "linkedin", "职位", "搜索结果", "工作"
            ]
            has_normal_content = any(keyword in content_lower for keyword in normal_keywords)

            # 如果既有风控关键词又有正常内容，可能是页面中的广告或其他内容
            if has_normal_content:
                logger.debug("检测到风控关键词，但页面包含正常LinkedIn内容，判断为正常页面")
                return False
            else:
                logger.warning("检测到风控关键词且无正常LinkedIn内容，判断为风控页面")
                return True

        return False

    def _check_job_content_loaded(self) -> bool:
        """检查职位内容是否已加载"""
        try:
            # 检查多种职位卡片选择器
            job_selectors = [
                ".base-search-card",
                "li[data-job-id]",
                ".job-card-container",
                ".job-card-list__item"
            ]

            for selector in job_selectors:
                job_cards = self.page.query_selector_all(selector)
                if len(job_cards) > 0:
                    logger.debug(f"使用选择器 {selector} 检测到 {len(job_cards)} 个职位卡片")
                    return True

            # 检查是否有职位搜索结果的容器
            container_selectors = [
                ".scaffold-layout__list-container",
                ".jobs-search-results-list",
                ".jobs-search__results-list"
            ]

            for selector in container_selectors:
                container = self.page.query_selector(selector)
                if container:
                    logger.debug(f"检测到职位容器: {selector}")
                    return True

            return False
        except Exception as e:
            logger.debug(f"检查职位内容时出错: {str(e)}")
            return False

    def search_jobs(self, keywords: str = None, location: str = None, easy_apply_only: bool = True) -> List[Dict]:
        """搜索职位（增强版：自动检测风控并重试，精准选择器+自动滚动加载）"""
        try:
            if not keywords:
                keywords = self.config['linkedin']['search_keywords'][0]
            if not location:
                location = self.config['linkedin']['location']

            logger.info(f"开始搜索职位: {keywords} in {location}")
            search_url = f"https://www.linkedin.com/jobs/search/?keywords={keywords}&location={location}"
            if easy_apply_only:
                search_url += "&f_AL=true"
            logger.debug(f"导航到搜索页面: {search_url}")

            # 确保浏览器已初始化
            if not self.page:
                self.setup_driver()

            # 自动检测风控并重试，增强：每次重试切换UA/代理，指数退避，goto后模拟人类操作，风控时截图
            max_retry = 3
            base_wait = 8
            retry_count = 0
            while retry_count < max_retry:
                try:
                    # 第一次尝试使用现有浏览器，后续重试时才重启浏览器
                    if retry_count > 0:
                        # 切换UA/代理并重启浏览器
                        ua = self._random_user_agent()
                        proxy = self._random_proxy()
                        self.setup_driver(headless=self.config['playwright']['headless'], user_agent=ua, proxy=proxy, force_restart=True)

                    self.page.goto(search_url)
                    self._simulate_human_actions()
                    try:
                        self.page.wait_for_selector(".scaffold-layout__list-container", timeout=20000)
                    except Exception:
                        pass
                    time.sleep(random.uniform(3, 6))
                    page_html = self.page.content()

                    # 检查页面是否成功加载
                    is_blocked = self.is_linkedin_blocked(page_html)
                    has_job_content = self._check_job_content_loaded()

                    if not is_blocked and has_job_content:
                        logger.info("页面成功加载，检测到职位内容")
                        break
                    elif not is_blocked and not has_job_content:
                        logger.debug("页面未被风控，但职位内容未加载完成，继续等待...")
                        # 如果不是风控但没有职位内容，可能还在加载，不算失败
                        if retry_count == 0:  # 第一次尝试时给更多时间
                            time.sleep(5)
                            has_job_content = self._check_job_content_loaded()
                            if has_job_content:
                                logger.info("延迟加载后检测到职位内容")
                                break
                    else:
                        logger.warning("检测到风控页面")
                        break  # 确实被风控了，需要重试
                except Exception as e:
                    logger.warning(f"访问搜索页面失败: {str(e)}")
                    # 访问失败，需要重试

                # 只有在确实被风控时才重试
                if is_blocked:
                    logger.warning(f"检测到LinkedIn风控页面，{retry_count+1}/{max_retry}，等待{base_wait * (2 ** retry_count)}秒后重试...（已截图）")
                    try:
                        self.page.screenshot(path=f"log/blocked_{int(time.time())}.png")
                    except Exception as e:
                        logger.warning(f"截图失败: {str(e)}")
                    time.sleep(base_wait * (2 ** retry_count))
                    retry_count += 1
                else:
                    # 没有被风控，可能只是加载慢，直接继续
                    logger.info("页面正常，继续处理职位列表")
                    break

            if retry_count == max_retry:
                logger.error("连续多次被LinkedIn风控，放弃本次抓取。")
                try:
                    self.page.screenshot(path="log/blocked_final.png")
                except Exception as e:
                    logger.warning(f"最终截图失败: {str(e)}")
                return self.parse_jobs_from_html("log/linkedin_jobs_full_page.html")

            try:
                # 等待页面加载完成
                logger.debug("等待页面加载...")
                self.page.wait_for_load_state("networkidle", timeout=20000)

                # 等待职位卡片出现（更宽松的等待）
                job_card_selectors = [
                    ".base-search-card",
                    "li[data-job-id]",
                    ".job-card-container",
                    ".job-card-list__item",
                    "[data-job-id]"  # 更通用的选择器
                ]

                job_cards_found = False
                for selector in job_card_selectors:
                    try:
                        self.page.wait_for_selector(selector, timeout=8000)
                        logger.debug(f"找到职位卡片: {selector}")
                        job_cards_found = True
                        break
                    except Exception:
                        continue

                if not job_cards_found:
                    logger.warning("未找到职位卡片，尝试等待更长时间...")
                    time.sleep(5)  # 额外等待时间

                # 自动滚动左侧职位列表，直到所有职位都加载出来
                logger.debug("开始滚动加载职位...")
                last_count = -1
                scroll_attempts = 0
                max_scrolls = 30

                for scroll_attempts in range(max_scrolls):
                    # 尝试多种职位卡片选择器（基于实际HTML结构）
                    job_card_selectors = [
                        ".base-search-card",  # LinkedIn当前使用的主要选择器
                        "[data-job-id]",  # 通用的job-id选择器
                        "li[data-job-id]",  # 列表项形式的job-id
                        ".job-card-container",  # 职位卡片容器
                        ".jobs-search-results__list-item",  # 搜索结果列表项
                        ".job-card-list__item"  # 职位卡片列表项
                    ]

                    job_cards = []
                    for selector in job_card_selectors:
                        job_cards = self.page.query_selector_all(selector)
                        if job_cards:
                            logger.debug(f"使用选择器 {selector} 找到 {len(job_cards)} 个职位卡片")
                            break

                    if len(job_cards) == last_count:
                        logger.debug(f"滚动 {scroll_attempts + 1} 次后职位数量未变化，停止滚动")
                        break  # 没有新职位加载，停止滚动

                    last_count = len(job_cards)

                    # 滚动到最后一个职位卡片
                    if job_cards:
                        try:
                            job_cards[-1].scroll_into_view_if_needed()
                        except Exception as e:
                            logger.debug(f"滚动到职位卡片失败: {str(e)}")

                    # 等待新内容加载
                    time.sleep(random.uniform(0.5, 1.0))

                # 保存页面HTML快照到 log 文件夹
                from pathlib import Path
                log_dir = Path(__file__).parent.parent / "log"
                log_dir.mkdir(parents=True, exist_ok=True)
                html_file_path = log_dir / "linkedin_jobs_playwright.html"
                page_content = self.page.content()
                with open(html_file_path, "w", encoding="utf-8") as f:
                    f.write(page_content)
                logger.info(f"页面HTML快照已保存到: {html_file_path}")

                # 使用LLM解析职位信息
                jobs = self._parse_jobs_with_llm_sync(page_content)

                if not jobs:
                    logger.warning("LLM解析未找到职位，尝试传统方法")
                    # 回退到传统解析方法
                    jobs = self._extract_jobs_traditional_sync(job_cards)

                logger.info(f"成功提取 {len(jobs)} 个职位信息")
                return jobs
            except Exception as e:
                logger.error(f"处理职位列表时出错: {str(e)}")
                with open("log/linkedin_jobs_full_page.html", "w", encoding="utf-8") as f:
                    f.write(f"<!-- 当前URL: {self.page.url} -->\n")
                    f.write(f"<!-- 时间戳: {time.strftime('%Y-%m-%d %H:%M:%S')} -->\n")
                    try:
                        cookies = self.context.cookies()
                        f.write(f"<!-- cookies: {cookies} -->\n")
                    except Exception as e:
                        f.write(f"<!-- cookies获取失败: {str(e)} -->\n")
                    f.write(self.page.content())
                page_text = self.page.content().lower()
                if any(x in page_text for x in ["captcha", "verify", "robot", "登录", "sign in", "security check"]):
                    logger.error("页面可能被反爬虫或登录失效拦截，请检查登录状态或增加等待时间")
                return []
        except Exception as e:
            logger.error(f"搜索职位失败: {str(e)}")
            return []

    def _extract_job_info(self, job_card) -> Optional[Dict]:
        """从职位卡片提取信息
        
        Args:
            job_card: Playwright ElementHandle对象
            
        Returns:
            职位信息字典
        """
        try:
            # 职位标题
            title_element = job_card.query_selector(
                ".job-card-list__title, .jobs-unified-top-card__job-title a, h3 a, a.job-card-container__link, a.job-card-list__title"
            )
            title = title_element.text_content().strip() if title_element else "未知职位"
            job_url = title_element.get_attribute('href') if title_element else None
            if job_url and not job_url.startswith('http'):
                job_url = f"https://www.linkedin.com{job_url}"
            
            # 公司名称
            company_element = job_card.query_selector(
                ".job-card-container__company-name, .jobs-unified-top-card__company-name a, .job-card-container__primary-description, .job-card-list__company-name"
            )
            company = company_element.text_content().strip() if company_element else "未知公司"
            
            # 地点
            try:
                location_element = job_card.query_selector(
                    ".job-card-container__metadata-item, .jobs-unified-top-card__bullet, .job-card-list__location"
                )
                location = location_element.text_content().strip() if location_element else "未知地点"
            except:
                location = "未知地点"
            
            # 检查是否为Easy Apply
            is_easy_apply = False
            try:
                easy_apply_element = job_card.query_selector(
                    ".jobs-apply-button--top-card, .job-card-container__apply-method, button:has-text('Easy Apply'), button:has-text('轻松申请')"
                )
                if easy_apply_element and ("Easy Apply" in easy_apply_element.text_content() or "轻松申请" in easy_apply_element.text_content()):
                    is_easy_apply = True
            except:
                pass
            
            return {
                'title': title,
                'company': company,
                'location': location,
                'url': job_url,
                'is_easy_apply': is_easy_apply,
                'job_id': self._extract_job_id(job_url) if job_url else str(hash(title+company+location))
            }
            
        except Exception as e:
            logger.warning(f"提取职位信息失败: {str(e)}")
            return None
    
    def _extract_job_info_precise(self, job_card) -> Optional[Dict]:
        """从LinkedIn职位卡片精确提取职位信息（适配新版LinkedIn结构）"""
        try:
            # 职位标题 - 使用LinkedIn实际选择器
            title_selectors = [
                ".base-search-card__title",  # LinkedIn实际使用的主要选择器
                "h3",  # 通用h3标题
                ".job-card-list__title",  # 旧版选择器（兼容）
                ".artdeco-entity-lockup__title strong"  # 备用选择器
            ]

            title_element = None
            title = "未知职位"
            job_url = None

            # 首先尝试从卡片链接获取URL
            link_selectors = [
                ".base-card__full-link",  # LinkedIn实际使用的卡片链接
                "a[href*='/jobs/view/']",  # 任何包含jobs/view的链接
                ".job-card-job-posting-card-wrapper__card-link",  # 备用选择器
                ".job-card-container__link"  # 旧版选择器
            ]

            for selector in link_selectors:
                link_element = job_card.query_selector(selector)
                if link_element:
                    job_url = link_element.get_attribute('href')
                    break

            # 然后获取职位标题
            for selector in title_selectors:
                title_element = job_card.query_selector(selector)
                if title_element:
                    title_text = title_element.text_content().strip()
                    # 过滤掉星号遮挡的内容（需要登录才能看到）
                    if title_text and not title_text.startswith('***'):
                        title = title_text
                        break

            if job_url and not job_url.startswith('http'):
                job_url = f"https://www.linkedin.com{job_url}"

            # 公司名称 - 使用LinkedIn实际选择器
            company_selectors = [
                ".base-search-card__subtitle a",  # LinkedIn实际使用的主要选择器
                ".base-search-card__subtitle",  # 备用选择器（无链接版本）
                ".artdeco-entity-lockup__subtitle",  # 旧版选择器
                ".job-card-container__company-name",
                ".job-card-list__company-name",
                "a[data-control-name='job_card_company_link']",
                "h4 a"
            ]

            company = "未知公司"
            for selector in company_selectors:
                company_element = job_card.query_selector(selector)
                if company_element:
                    company_text = company_element.text_content().strip()
                    # 过滤掉星号遮挡的内容
                    if company_text and not company_text.startswith('***'):
                        company = company_text
                        break

            # 地点信息 - 使用LinkedIn实际选择器
            location_selectors = [
                ".job-search-card__location",  # LinkedIn实际使用的主要选择器
                ".base-search-card__metadata span",  # 备用选择器
                ".artdeco-entity-lockup__caption",  # 旧版选择器
                ".job-card-container__metadata-item",
                ".job-card-list__location",
                ".base-search-card__metadata"
            ]

            location = "未知地点"
            for selector in location_selectors:
                location_element = job_card.query_selector(selector)
                if location_element:
                    location_text = location_element.text_content().strip()
                    # 过滤掉时间信息，只保留地点
                    if location_text and not any(time_word in location_text.lower() for time_word in ['ago', 'day', 'week', 'month', 'hour', '前', '小时', '天', '周', '月']):
                        location = location_text
                        break

            # Easy Apply检测 - 使用LinkedIn实际结构
            is_easy_apply = False

            # 方法1: 检查整个卡片的HTML内容（最可靠的方法）
            try:
                card_html = job_card.inner_html()
                # 检查多种Easy Apply相关的文本
                easy_apply_indicators = [
                    "Easy Apply", "轻松申请", "抢先申请", "正在招聘",
                    "job-posting-benefits", "Apply now"
                ]

                for indicator in easy_apply_indicators:
                    if indicator in card_html:
                        is_easy_apply = True
                        break
            except Exception:
                pass

            # 方法2: 查找Easy Apply相关元素
            if not is_easy_apply:
                easy_apply_selectors = [
                    ".job-posting-benefits",  # LinkedIn实际使用的福利标识
                    "span:has-text('Easy Apply')",
                    "span:has-text('轻松申请')",
                    "span:has-text('抢先申请')",
                    "span:has-text('正在招聘')",
                    ".job-card-container__apply-method",
                    ".job-card-list__easy-apply-label",
                    "button:has-text('Easy Apply')",
                    "[data-control-name='job_card_easy_apply']"
                ]

                for selector in easy_apply_selectors:
                    try:
                        easy_apply_element = job_card.query_selector(selector)
                        if easy_apply_element:
                            is_easy_apply = True
                            break
                    except Exception:
                        continue

            # 生成job_id
            job_id = self._extract_job_id(job_url) if job_url else str(hash(title + company + location))

            job_info = {
                'title': title,
                'company': company,
                'location': location,
                'url': job_url,
                'is_easy_apply': is_easy_apply,
                'job_id': job_id
            }

            logger.debug(f"提取职位信息: {title} @ {company} | Easy Apply: {is_easy_apply}")
            return job_info

        except Exception as e:
            logger.warning(f"精确提取职位信息失败: {str(e)}")
            return None
    
    def apply_to_job(self, job_info: Dict) -> bool:
        """申请职位
        
        Args:
            job_info: 职位信息字典
            
        Returns:
            是否申请成功
        """
        try:
            job_id = job_info['job_id']
            
            # 检查是否已申请
            if job_id in self.applied_jobs:
                logger.info(f"职位 {job_info['title']} 已申请过，跳过")
                return False
            
            if not job_info['is_easy_apply']:
                logger.info(f"职位 {job_info['title']} 不支持Easy Apply，跳过")
                return False
            
            logger.info(f"开始申请职位: {job_info['title']} at {job_info['company']}")
            
            # 打开职位页面
            self.page.goto(job_info['url'], wait_until="networkidle")
            self._simulate_human_actions()
            time.sleep(2)
            
            # 查找并点击Easy Apply按钮
            try:
                self.page.wait_for_selector("button:has-text('Easy Apply')", timeout=10000)
                self.page.click("button:has-text('Easy Apply')")
                self._simulate_human_actions()
                time.sleep(2)
                
            except PlaywrightTimeoutError:
                logger.warning(f"未找到Easy Apply按钮: {job_info['title']}")
                try:
                    self.page.screenshot(path=f"log/blocked_apply_{int(time.time())}.png")
                except Exception as e:
                    logger.warning(f"申请页截图失败: {str(e)}")
                return False
            
            # 处理申请流程
            success = self._handle_application_process()
            
            if success:
                self.applied_jobs.add(job_id)
                logger.info(f"成功申请职位: {job_info['title']}")
                
                # 随机延迟
                delay = random.randint(*self.config['linkedin']['delay_between_applications'])
                logger.info(f"等待 {delay} 秒后继续...")
                time.sleep(delay)
                
            return success
            
        except Exception as e:
            logger.error(f"申请职位失败 {job_info['title']}: {str(e)}")
            return False
    
    def _handle_application_process(self) -> bool:
        """处理申请流程

        Returns:
            是否申请成功
        """
        try:
            max_steps = 8  # 增加最大步骤数
            current_step = 0

            logger.info("开始处理LinkedIn申请流程...")
            
            while current_step < max_steps:
                logger.info(f"申请流程步骤 {current_step + 1}/{max_steps}")
                time.sleep(3)  # 增加等待时间
                
                # 检查是否有问题需要回答
                if self._answer_application_questions():
                    current_step += 1
                    continue
                
                # 使用智能按钮检测
                button, button_type = self._find_application_button()

                if button and button_type == 'submit':
                    logger.info("找到提交按钮，正在提交申请...")
                    button.click()
                    time.sleep(3)

                    # 检查是否申请成功
                    success_message = self.page.query_selector("text=Application sent, text=申请已发送, text=Your application was sent")
                    if success_message:
                        logger.info("申请成功提交")
                        return True

                    logger.info("未找到成功消息，但申请可能已提交")
                    return True

                elif button and button_type in ['next', 'review']:
                    button_text = button.text_content() or button.get_attribute('aria-label') or f'{button_type} button'
                    logger.info(f"点击{button_type}按钮: {button_text}")
                    button.click()
                    current_step += 1
                    time.sleep(3)  # 增加等待时间

                else:
                    # 检查是否有错误或需要手动处理
                    logger.warning(f"申请流程步骤 {current_step}: 未找到可点击的按钮，可能需要手动处理")

                    # 尝试查找其他可能的按钮
                    all_buttons = self.page.query_selector_all("button")
                    visible_buttons = [btn for btn in all_buttons if btn.is_visible() and btn.is_enabled()]

                    if visible_buttons:
                        logger.info(f"找到 {len(visible_buttons)} 个可见按钮")
                        for i, btn in enumerate(visible_buttons[:3]):  # 只显示前3个
                            btn_text = btn.text_content() or btn.get_attribute('aria-label') or f'Button {i+1}'
                            logger.info(f"  按钮 {i+1}: {btn_text}")

                    break
            
            logger.warning("申请流程未能完成，可能需要手动处理")
            return False

        except Exception as e:
            logger.error(f"处理申请流程失败: {str(e)}")
            return False

    def _find_application_button(self) -> tuple:
        """智能查找申请流程中的按钮

        Returns:
            (button_element, button_type) - button_type可以是'next', 'submit', 'review'
        """
        try:
            # 定义按钮选择器和类型
            button_selectors = [
                # 提交按钮 - 优先级最高
                ("button[aria-label='Submit application']", 'submit'),
                ("button:has-text('Submit application')", 'submit'),
                ("button:has-text('Submit')", 'submit'),
                ("button:has-text('提交')", 'submit'),

                # Review按钮
                ("button:has-text('Review')", 'review'),
                ("button:has-text('审核')", 'review'),

                # Next按钮
                ("button[aria-label='Continue to next step']", 'next'),
                ("button:has-text('Next')", 'next'),
                ("button:has-text('下一步')", 'next'),
            ]

            for selector, button_type in button_selectors:
                try:
                    button = self.page.query_selector(selector)
                    if button and button.is_visible() and button.is_enabled():
                        button_text = button.text_content() or button.get_attribute('aria-label') or ''
                        logger.info(f"找到{button_type}按钮: {button_text}")
                        return button, button_type
                except Exception:
                    continue

            return None, None

        except Exception as e:
            logger.warning(f"查找申请按钮失败: {str(e)}")
            return None, None
    
    def _answer_application_questions(self) -> bool:
        """回答申请问题
        
        Returns:
            是否回答了问题
        """
        try:
            answered = False
            
            # 查找文本输入框
            text_inputs = self.page.query_selector_all(
                "input[type='text'], input[type='number'], textarea"
            )
            
            for input_field in text_inputs:
                if input_field.is_visible() and input_field.is_enabled():
                    placeholder = input_field.get_attribute('placeholder') or ''
                    label = self._get_field_label(input_field)
                    
                    answer = self._get_answer_for_question(placeholder + ' ' + label)
                    if answer and not input_field.input_value():
                        input_field.fill(answer)
                        answered = True
            
            # 查找单选按钮
            radio_groups = self.page.query_selector_all(
                "fieldset, .fb-radio-buttons"
            )
            
            for group in radio_groups:
                if group.is_visible():
                    radios = group.query_selector_all("input[type='radio']")
                    if radios:
                        # 检查是否已有选中的选项
                        selected = False
                        for radio in radios:
                            if radio.is_checked():
                                selected = True
                                break
                        
                        if not selected and len(radios) > 0:
                            # 选择第一个选项（通常是"是"或积极的回答）
                            radios[0].check()
                            answered = True
            
            # 查找下拉选择框
            selects = self.page.query_selector_all("select")
            for select in selects:
                if select.is_visible() and select.is_enabled():
                    # 检查是否已有选中的选项
                    value = select.evaluate("el => el.value")
                    if not value:
                        # 获取所有选项
                        options = select.query_selector_all("option")
                        if len(options) > 1:
                            # 选择第二个选项（跳过默认的空选项）
                            options[1].evaluate("option => option.selected = true")
                            select.evaluate("select => select.dispatchEvent(new Event('change'))")
                            answered = True
            
            return answered
            
        except Exception as e:
            logger.warning(f"回答申请问题失败: {str(e)}")
            return False
    
    def _get_field_label(self, input_field) -> str:
        """获取输入框的标签
        
        Args:
            input_field: Playwright ElementHandle对象
            
        Returns:
            标签文本
        """
        try:
            # 尝试多种方式获取标签
            label_id = input_field.get_attribute('aria-labelledby')
            if label_id:
                label_element = self.page.query_selector(f"#{label_id}")
                if label_element:
                    return label_element.text_content()
            
            # 查找相邻的label元素
            parent = input_field.evaluate("el => el.parentElement")
            if parent:
                label = self.page.evaluate("parent => parent.querySelector('label')", parent)
                if label:
                    return self.page.evaluate("label => label.textContent", label)
            
        except:
            pass
        
        return ''
    
    def _get_answer_for_question(self, question_text: str) -> str:
        """根据问题文本获取答案
        
        Args:
            question_text: 问题文本
            
        Returns:
            答案文本
        """
        question_lower = question_text.lower()
        default_answers = self.config['linkedin']['default_answers']
        
        if 'experience' in question_lower or '经验' in question_lower:
            return default_answers.get('years_experience', '3')
        elif 'relocate' in question_lower or '搬迁' in question_lower:
            return default_answers.get('willing_to_relocate', 'Yes')
        elif 'authorized' in question_lower or 'work authorization' in question_lower:
            return default_answers.get('authorized_to_work', 'Yes')
        elif 'sponsorship' in question_lower or '赞助' in question_lower:
            return default_answers.get('require_sponsorship', 'No')
        elif 'phone' in question_lower or '电话' in question_lower:
            return '1234567890'  # 示例电话号码
        
        return ''
    
    def batch_apply_jobs(self, max_applications: int = None) -> Dict:
        """批量申请职位
        
        Args:
            max_applications: 最大申请数量
            
        Returns:
            申请结果字典
        """
        if not max_applications:
            max_applications = self.config['linkedin']['max_applications_per_day']
        
        results = {
            'total_found': 0,
            'total_applied': 0,
            'successful_applications': [],
            'failed_applications': []
        }
        
        try:
            # 搜索职位
            for keyword in self.config['linkedin']['search_keywords']:
                if results['total_applied'] >= max_applications:
                    break
                    
                jobs = self.search_jobs(keyword)
                results['total_found'] += len(jobs)
                
                for job in jobs:
                    if results['total_applied'] >= max_applications:
                        break
                    
                    if self.apply_to_job(job):
                        results['total_applied'] += 1
                        results['successful_applications'].append(job)
                    else:
                        results['failed_applications'].append(job)
            
            logger.info(f"批量申请完成: 找到 {results['total_found']} 个职位，成功申请 {results['total_applied']} 个")
            return results
            
        except Exception as e:
            logger.error(f"批量申请失败: {str(e)}")
            return results
    
    def close(self):
        """关闭浏览器"""
        try:
            if self.context:
                self.context.close()
                self.context = None
            
            if self.browser:
                self.browser.close()
                self.browser = None
            
            if self.playwright:
                self.playwright.stop()
                self.playwright = None
                
            logger.info("浏览器已关闭")
        except Exception as e:
            logger.error(f"关闭浏览器失败: {str(e)}")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
        return False  # 不抑制异常

    def _extract_job_id(self, job_url: str) -> str:
        """从职位URL中提取职位ID

        Args:
            job_url: 职位URL

        Returns:
            职位ID字符串
        """
        try:
            if not job_url:
                return ""

            # LinkedIn职位URL格式通常为: https://www.linkedin.com/jobs/view/1234567890/
            import re
            match = re.search(r'/jobs/view/(\d+)', job_url)
            if match:
                return match.group(1)

            # 如果没有找到标准格式，尝试其他模式
            match = re.search(r'jobId=(\d+)', job_url)
            if match:
                return match.group(1)

            # 如果都没找到，返回URL的hash值
            return str(hash(job_url))

        except Exception as e:
            logger.warning(f"提取职位ID失败: {str(e)}")
            return str(hash(job_url)) if job_url else ""

    def _simulate_human_actions(self):
        """模拟人类行为，避免被检测为机器人"""
        try:
            # 随机移动鼠标
            import random
            if self.page:
                # 获取页面尺寸
                viewport = self.page.viewport_size
                if viewport:
                    # 随机移动鼠标到页面中的某个位置
                    x = random.randint(100, viewport['width'] - 100)
                    y = random.randint(100, viewport['height'] - 100)
                    self.page.mouse.move(x, y)

                    # 随机等待
                    time.sleep(random.uniform(0.5, 1.5))

                    # 偶尔滚动页面
                    if random.random() < 0.3:  # 30%的概率滚动
                        scroll_amount = random.randint(100, 300)
                        self.page.mouse.wheel(0, scroll_amount)
                        time.sleep(random.uniform(0.5, 1.0))

        except Exception as e:
            logger.debug(f"模拟人类行为时出错: {str(e)}")

    def parse_jobs_from_html(self, html_path: str = "log/linkedin_jobs_full_page.html") -> list:
        """从本地HTML文件解析职位信息列表（适配新版LinkedIn结构）
        Args:
            html_path: HTML文件路径
        Returns:
            职位信息字典列表
        """
        from bs4 import BeautifulSoup
        jobs = []
        try:
            with open(html_path, "r", encoding="utf-8") as f:
                html = f.read()
            soup = BeautifulSoup(html, "html.parser")

            # 使用LinkedIn实际选择器
            job_cards = soup.select(".base-search-card")  # LinkedIn实际使用的主要选择器
            if not job_cards:
                logger.warning("未找到.base-search-card职位卡片，尝试其他选择器...")
                job_cards = soup.select("li[data-job-id]")
            if not job_cards:
                logger.warning("未找到li[data-job-id]职位卡片，尝试旧版结构...")
                job_cards = soup.select("li.job-card-container.jobs-search-results__list-item")
            if not job_cards:
                logger.warning("未找到标准职位卡片，尝试通用div元素...")
                # 过滤掉导航元素，只保留包含职位信息的元素
                all_divs = soup.select("div.base-card")
                job_cards = []
                for div in all_divs:
                    # 检查是否包含职位相关的类名或结构
                    if (div.select_one(".base-search-card__title") or
                        div.select_one("a[href*='/jobs/view/']") or
                        div.get('data-entity-urn')):
                        job_cards.append(div)

            logger.info(f"HTML解析共发现{len(job_cards)}个职位卡片候选")

            for card in job_cards:
                # 职位标题 - 使用LinkedIn实际选择器
                title_elem = card.select_one(".base-search-card__title, h3, .job-card-list__title")
                title = title_elem.get_text(strip=True) if title_elem else "未知职位"

                # 过滤掉星号遮挡的内容
                if title.startswith('***'):
                    title = "未知职位"

                # 获取职位URL
                job_url = None
                link_elem = card.select_one(".base-card__full-link, a[href*='/jobs/view/']")
                if link_elem and link_elem.has_attr('href'):
                    job_url = link_elem['href']
                    if job_url and not job_url.startswith('http'):
                        job_url = f"https://www.linkedin.com{job_url}"

                # 公司名称 - 使用LinkedIn实际选择器
                company_elem = card.select_one(".base-search-card__subtitle a, .base-search-card__subtitle")
                company = company_elem.get_text(strip=True) if company_elem else "未知公司"

                # 过滤掉星号遮挡的内容
                if company.startswith('***'):
                    company = "未知公司"

                # 地点信息 - 使用LinkedIn实际选择器
                location_elem = card.select_one(".job-search-card__location, .base-search-card__metadata span")
                location = location_elem.get_text(strip=True) if location_elem else "未知地点"

                # Easy Apply检测
                is_easy_apply = False
                card_html = str(card)
                easy_apply_indicators = ["Easy Apply", "轻松申请", "抢先申请", "正在招聘", "job-posting-benefits"]
                for indicator in easy_apply_indicators:
                    if indicator in card_html:
                        is_easy_apply = True
                        break

                # 只添加有效的职位信息（过滤掉导航元素）
                if (title != "未知职位" and
                    title not in ["Home", "My Network", "Jobs", "Messaging", "Notifications"] and
                    not title.startswith("new feed updates") and
                    job_url and "/jobs/view/" in job_url):

                    jobs.append({
                        'title': title,
                        'company': company,
                        'location': location,
                        'url': job_url,
                        'is_easy_apply': is_easy_apply,
                        'job_id': self._extract_job_id(job_url) if job_url else str(hash(title + company + location))
                    })

            logger.info(f"HTML解析完成，提取到{len(jobs)}个有效职位")
            if not jobs:
                logger.error("HTML解析未能提取到任何有效职位信息，请检查快照文件内容或结构！")
            return jobs
        except Exception as e:
            logger.error(f"解析本地HTML职位信息失败: {str(e)}")
            return []


# 使用示例
if __name__ == "__main__":
    # 创建配置文件示例
    config = {
        'linkedin': {
            'email': '<EMAIL>',
            'password': 'your_password',
            'search_keywords': ['python developer', 'software engineer', 'data scientist'],
            'location': 'United States',
            'max_applications_per_day': 20,
            'delay_between_applications': [30, 60],
            'auto_answer_questions': True,
            'default_answers': {
                'years_experience': '3',
                'willing_to_relocate': 'Yes',
                'authorized_to_work': 'Yes',
                'require_sponsorship': 'No'
            }
        },
        'playwright': {
            'headless': False,
            'timeout': 30000,
            'window_size': [1200, 800]
        }
    }
    
    # 保存配置文件
    with open('linkedin_config.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    # 使用自动化工具
    with LinkedInAutomationPlaywright('linkedin_config.yaml') as linkedin:
        linkedin.setup_driver()
        
        login_result = linkedin.login()
        if login_result['success']:
            results = linkedin.batch_apply_jobs(max_applications=10)
            print(f"申请结果: {results}")

    def _parse_jobs_with_llm_sync(self, page_source: str) -> List[Dict]:
        """同步使用LLM解析职位信息"""
        try:
            logger.info("开始使用LLM解析职位信息...")

            # 获取API密钥
            api_key = self._get_api_key()
            if not api_key:
                logger.error("未找到API密钥，无法使用LLM解析")
                return []

            # 导入LLM相关模块
            from langchain_google_genai import ChatGoogleGenerativeAI
            from langchain_core.prompts import ChatPromptTemplate
            from langchain_core.output_parsers import StrOutputParser
            import json

            # 初始化LLM
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.5-flash-preview-05-20",
                google_api_key=api_key,
                temperature=1.0,  # 使用用户偏好的温度设置
                request_timeout=60.0,
                max_retries=3,
                transport="rest"
            )

            # 创建解析提示
            prompt_template = ChatPromptTemplate.from_template("""
你是一个专业的LinkedIn职位信息解析专家。请从以下HTML页面源码中提取所有职位信息。

请仔细分析HTML内容，找出所有职位卡片，并提取以下信息：
1. 职位标题 (title)
2. 公司名称 (company)
3. 职位地点 (location)
4. 职位链接 (url) - 完整的LinkedIn职位链接
5. 是否支持Easy Apply (is_easy_apply) - true/false

请以JSON数组格式返回结果，每个职位一个对象。如果某个字段无法找到，请使用合理的默认值。

HTML内容：
{html_content}

请只返回JSON数组，不要包含任何其他文本或解释。格式如下：
[
  {{
    "title": "职位标题",
    "company": "公司名称",
    "location": "职位地点",
    "url": "https://www.linkedin.com/jobs/view/职位ID",
    "is_easy_apply": true,
    "job_id": "职位ID"
  }}
]
""")

            # 截取HTML内容的关键部分以避免token限制
            truncated_html = self._truncate_html_for_llm(page_source)

            # 创建链式处理
            chain = prompt_template | llm | StrOutputParser()

            # 调用LLM
            logger.info("正在调用LLM解析职位信息...")
            response = chain.invoke({"html_content": truncated_html})

            # 解析JSON响应
            try:
                # 清理响应文本，移除可能的markdown标记
                clean_response = response.strip()
                if clean_response.startswith("```json"):
                    clean_response = clean_response[7:]
                if clean_response.endswith("```"):
                    clean_response = clean_response[:-3]
                clean_response = clean_response.strip()

                jobs_data = json.loads(clean_response)

                # 验证和清理数据
                validated_jobs = []
                for job in jobs_data:
                    if isinstance(job, dict) and job.get('title') and job.get('company'):
                        # 确保job_id存在
                        if not job.get('job_id'):
                            job['job_id'] = self._extract_job_id(job.get('url', '')) or str(hash(job.get('title', '') + job.get('company', '')))

                        # 确保URL是完整的LinkedIn链接
                        if job.get('url') and not job['url'].startswith('http'):
                            if job['url'].startswith('/'):
                                job['url'] = 'https://www.linkedin.com' + job['url']

                        validated_jobs.append(job)

                logger.info(f"LLM成功解析出 {len(validated_jobs)} 个职位")
                return validated_jobs

            except json.JSONDecodeError as e:
                logger.error(f"LLM响应JSON解析失败: {str(e)}")
                logger.debug(f"LLM原始响应: {response[:500]}...")
                return []

        except Exception as e:
            logger.error(f"LLM解析职位信息失败: {str(e)}")
            return []

    def _get_api_key(self) -> str:
        """获取API密钥"""
        try:
            # 尝试从多个来源获取API密钥
            import os
            from pathlib import Path
            import yaml

            # 1. 从环境变量获取
            api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
            if api_key:
                return api_key

            # 2. 从secrets.yaml文件获取
            secrets_file = Path(__file__).parent.parent / "secrets.yaml"
            if secrets_file.exists():
                with open(secrets_file, 'r', encoding='utf-8') as f:
                    secrets = yaml.safe_load(f)
                    api_key = secrets.get('gemini_api_key') or secrets.get('google_api_key') or secrets.get('llm_api_key')
                    if api_key:
                        return api_key

            # 3. 从data_folder/secrets.yaml获取
            data_secrets_file = Path(__file__).parent.parent / "data_folder" / "secrets.yaml"
            if data_secrets_file.exists():
                with open(data_secrets_file, 'r', encoding='utf-8') as f:
                    secrets = yaml.safe_load(f)
                    api_key = secrets.get('gemini_api_key') or secrets.get('google_api_key') or secrets.get('llm_api_key')
                    if api_key:
                        return api_key

            logger.warning("未找到API密钥")
            return None

        except Exception as e:
            logger.error(f"获取API密钥时出错: {str(e)}")
            return None

    def _truncate_html_for_llm(self, html_content: str, max_chars: int = 2000000) -> str:
        """截取HTML内容以适应LLM token限制"""
        if len(html_content) <= max_chars:
            return html_content

        # 尝试找到职位相关的部分
        job_keywords = ['job', 'position', 'career', 'work', 'employment', 'base-search-card', 'job-card']

        # 查找包含职位关键词的部分
        best_start = 0
        best_score = 0

        for i in range(0, len(html_content) - max_chars, 1000):
            chunk = html_content[i:i + max_chars].lower()
            score = sum(chunk.count(keyword) for keyword in job_keywords)
            if score > best_score:
                best_score = score
                best_start = i

        truncated = html_content[best_start:best_start + max_chars]
        logger.info(f"HTML内容已截取: {len(html_content)} -> {len(truncated)} 字符")
        return truncated

    def _extract_jobs_traditional_sync(self, job_cards) -> List[Dict]:
        """同步传统方法提取职位信息（作为LLM解析的备用方案）"""
        try:
            logger.info("使用传统方法提取职位信息...")

            jobs = []
            for card in job_cards[:20]:  # 限制前20个职位
                try:
                    job_info = self._extract_job_info_precise(card)
                    if job_info:
                        jobs.append(job_info)
                except Exception as e:
                    logger.warning(f"提取职位信息失败: {str(e)}")
                    continue

            logger.info(f"传统方法成功提取 {len(jobs)} 个职位")
            return jobs

        except Exception as e:
            logger.error(f"传统方法提取职位失败: {str(e)}")
            return []

    def _extract_job_id(self, url: str) -> str:
        """从URL中提取职位ID"""
        try:
            import re
            if url:
                # 匹配LinkedIn职位URL中的ID
                match = re.search(r'/jobs/view/(\d+)', url)
                if match:
                    return match.group(1)
            return None
        except Exception:
            return None

LinkedInAutomation = LinkedInAutomationPlaywright