"""LinkedIn自动化API端点"""

from typing import List, Dict, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from loguru import logger
import sys
import os
from pathlib import Path # 确保导入Path
import time
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到Python路径
# __file__ 指向 d:\Jobs_Applier_AI_Agent_AIHawk-main\webui\backend\linkedin_api.py
# .resolve().parents[2] 会得到项目根目录 d:\Jobs_Applier_AI_Agent_AIHawk-main
project_root_dir = Path(__file__).resolve().parents[2]

if str(project_root_dir) not in sys.path:
    sys.path.insert(0, str(project_root_dir))

try:
    from src.linkedin_automation import LinkedInAutomation as SeleniumAutomation
    logger.info("Selenium自动化类导入成功")
except ImportError as e:
    logger.warning(f"Selenium自动化类导入失败: {e}")
    SeleniumAutomation = None

try:
    from src.linkedin_automation_playwright import LinkedInAutomationPlaywright as PlaywrightAutomation
    logger.info("Playwright同步自动化类导入成功")
except ImportError as e:
    logger.warning(f"Playwright同步自动化类导入失败: {e}")
    PlaywrightAutomation = None

try:
    from src.linkedin_automation_playwright_async import LinkedInAutomationPlaywrightAsync as PlaywrightAsyncAutomation
    logger.info("Playwright异步自动化类导入成功")
except ImportError as e:
    logger.warning(f"Playwright异步自动化类导入失败: {e}")
    PlaywrightAsyncAutomation = None

# 根据配置文件选择默认自动化工具
try:
    import yaml
    config_path = Path(__file__).resolve().parents[2] / "linkedin_config.yaml"
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            automation_tool = config.get('automation_tool', 'selenium')
            if automation_tool == 'playwright' and PlaywrightAutomation:
                LinkedInAutomation = PlaywrightAutomation
                logger.info("根据配置文件使用 Playwright 自动化工具")
            elif automation_tool == 'playwright_async' and PlaywrightAsyncAutomation:
                LinkedInAutomation = PlaywrightAsyncAutomation
                logger.info("根据配置文件使用 Playwright 异步自动化工具")
            else:
                LinkedInAutomation = SeleniumAutomation
                logger.info("根据配置文件使用 Selenium 自动化工具")
    else:
        LinkedInAutomation = SeleniumAutomation  # 默认Selenium
        logger.info("配置文件不存在，使用默认 Selenium 自动化工具")
except Exception as e:
    logger.warning(f"读取配置文件失败，使用默认 Selenium: {e}")
    LinkedInAutomation = SeleniumAutomation

router = APIRouter(prefix="/api/linkedin", tags=["LinkedIn自动化"])

# 配置日志记录器
log_file_path = Path(__file__).parent / "log" / "linkedin_automation.log"
log_file_path.parent.mkdir(parents=True, exist_ok=True)

# 配置loguru记录器，同时输出到控制台和文件
logger.remove()  # 移除默认处理器
logger.add(sys.stderr, level="INFO")  # 添加标准错误输出处理器
logger.add(
    str(log_file_path),
    rotation="10 MB",  # 日志文件大小达到10MB时轮转
    retention="1 week",  # 保留1周的日志
    compression="zip",  # 压缩旧日志
    level="DEBUG",  # 记录DEBUG及以上级别的日志
    format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
)

logger.info(f"日志文件路径: {log_file_path}")

# 全局LinkedIn自动化实例
linkedin_automation = None
automation_status = {
    "is_running": False,
    "is_logged_in": False,
    "current_task": None,
    "automation_type": "selenium",  # 默认使用selenium，可选"playwright", "playwright_async"
    "progress": {
        "total_found": 0,
        "total_applied": 0,
        "successful_applications": [],
        "failed_applications": []
    }
}

# 线程池执行器，用于在同步环境中运行 Playwright
executor = ThreadPoolExecutor(max_workers=1)

def run_in_thread(func, *args, **kwargs):
    """在线程池中运行同步函数"""
    return func(*args, **kwargs)

# Pydantic模型
class LoginRequest(BaseModel):
    email: str = Field(..., description="LinkedIn邮箱")
    password: str = Field(..., description="LinkedIn密码")
    headless: bool = Field(default=False, description="是否无头模式")

class SearchRequest(BaseModel):
    keywords: str = Field(..., description="搜索关键词")
    location: str = Field(default="United States", description="搜索地点")
    easy_apply_only: bool = Field(default=True, description="仅Easy Apply职位")

class BatchApplyRequest(BaseModel):
    max_applications: int = Field(default=20, description="最大申请数量")
    keywords: List[str] = Field(default=["python developer"], description="搜索关键词列表")
    location: str = Field(default="United States", description="搜索地点")

class JobInfo(BaseModel):
    title: str
    company: str
    location: str
    url: str
    is_easy_apply: bool
    job_id: str

class SetupRequest(BaseModel):
    headless: bool = Field(default=False, description="是否无头模式")
    automation_type: str = Field(default="playwright_async", description="自动化类型: selenium, playwright, playwright_async")

class AutomationStatus(BaseModel):
    is_running: bool
    is_logged_in: bool
    current_task: Optional[str]
    progress: Dict

# API端点
@router.get("/status", response_model=AutomationStatus)
async def get_automation_status():
    """获取自动化状态"""
    return AutomationStatus(**automation_status)

@router.post("/setup")
async def setup_automation(request: SetupRequest):
    """设置LinkedIn自动化，支持Selenium/Playwright/PlaywrightAsync切换"""
    global linkedin_automation, LinkedInAutomation, automation_status

    automation_type = request.automation_type
    headless = request.headless

    if automation_type == "playwright_async":
        LinkedInAutomation = PlaywrightAsyncAutomation
        automation_status["automation_type"] = "playwright_async"
    elif automation_type == "playwright":
        LinkedInAutomation = PlaywrightAutomation
        automation_status["automation_type"] = "playwright"
    else:
        LinkedInAutomation = SeleniumAutomation
        automation_status["automation_type"] = "selenium"

    if LinkedInAutomation is None:
        raise HTTPException(status_code=500, detail="LinkedIn自动化模块未正确导入，请检查依赖安装")

    try:
        logger.info(f"开始设置 {automation_type} 自动化...")

        if linkedin_automation:
            logger.info("关闭现有自动化实例...")
            old_automation = linkedin_automation
            linkedin_automation = None  # 先清空全局变量

            try:
                if automation_status.get("automation_type") == "playwright_async":
                    # 直接调用异步 close 方法
                    if hasattr(old_automation, 'close') and old_automation.close:
                        await old_automation.close()
                elif automation_status.get("automation_type") == "playwright":
                    # 在线程池中运行 Playwright 的 close 方法
                    if hasattr(old_automation, 'close') and old_automation.close:
                        await asyncio.get_event_loop().run_in_executor(executor, old_automation.close)
                else:
                    # Selenium
                    if hasattr(old_automation, 'close') and old_automation.close:
                        old_automation.close()
                logger.info("现有自动化实例已关闭")
            except Exception as close_error:
                logger.warning(f"关闭现有自动化实例时出错: {close_error}")
                # 继续执行，不要因为关闭失败而中断设置过程

        config_path = os.path.join(
            os.path.dirname(__file__), '..', '..', 'linkedin_config.yaml'
        )
        logger.info(f"配置文件路径: {config_path}")
        logger.info(f"创建 {automation_type} 自动化实例...")

        # 根据自动化类型创建不同的实例
        if automation_type == "playwright_async":
            linkedin_automation = PlaywrightAsyncAutomation(config_path)
        elif automation_type == "playwright":
            linkedin_automation = PlaywrightAutomation(config_path)
        else:  # selenium
            linkedin_automation = SeleniumAutomation(config_path)

        logger.info(f"自动化实例创建成功: {type(linkedin_automation)}")

        if automation_type == "playwright_async":
            logger.info("设置异步 Playwright 浏览器...")
            logger.info(f"当前事件循环: {asyncio.get_event_loop()}")
            logger.info(f"是否在主线程: {threading.current_thread() == threading.main_thread()}")

            # 尝试关闭可能存在的 Selenium WebDriver 以避免冲突
            try:
                from webui.backend.main import resume_facade
                if hasattr(resume_facade, 'driver') and resume_facade.driver:
                    logger.info("检测到 Selenium WebDriver，尝试关闭以避免冲突...")
                    resume_facade.driver.quit()
                    resume_facade.driver = None
                    logger.info("Selenium WebDriver 已关闭")
            except Exception as selenium_close_error:
                logger.warning(f"关闭 Selenium WebDriver 时出错: {selenium_close_error}")

            # 尝试设置浏览器
            try:
                # 直接调用异步方法
                await linkedin_automation.setup_driver(headless=headless)
                logger.info("异步 Playwright 浏览器设置完成")
            except Exception as setup_error:
                logger.error(f"浏览器设置失败，详细错误: {setup_error}")
                logger.error(f"错误类型: {type(setup_error)}")

                # 尝试重新创建实例
                logger.info("尝试重新创建自动化实例...")
                try:
                    await linkedin_automation.close()
                    linkedin_automation = PlaywrightAsyncAutomation(config_path)
                    await linkedin_automation.setup_driver(headless=headless)
                    logger.info("重新创建成功")
                except Exception as retry_error:
                    logger.error(f"重新创建也失败: {retry_error}")
                    raise setup_error
        elif automation_type == "playwright":
            logger.info("设置同步 Playwright 浏览器...")
            # 在新线程中运行 Playwright 的 setup_driver 方法，避免 asyncio 循环冲突
            def setup_playwright():
                logger.info(f"在线程中设置 Playwright，headless={headless}")
                try:
                    # 在新线程中，没有 asyncio 循环，可以安全使用同步 Playwright
                    result = linkedin_automation.setup_driver(headless=headless)
                    logger.info(f"Playwright 设置完成，返回: {type(result)}")
                    return result
                except Exception as e:
                    logger.error(f"线程中设置 Playwright 失败: {e}")
                    raise e

            # 使用 ThreadPoolExecutor 在新线程中运行
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as thread_executor:
                future = thread_executor.submit(setup_playwright)
                # 等待线程完成
                result = future.result(timeout=60)  # 60秒超时
            logger.info("同步 Playwright 浏览器设置完成")
        else:
            logger.info("设置 Selenium 浏览器...")
            linkedin_automation.setup_driver(headless=headless)
            logger.info("Selenium 浏览器设置完成")

        automation_status["current_task"] = "已设置浏览器"
        logger.info(f"{automation_type} 自动化设置成功")
        return {"success": True, "message": f"LinkedIn自动化设置成功，当前类型: {automation_type}"}
    except Exception as e:
        logger.error(f"设置LinkedIn自动化失败: {str(e)}")
        logger.error(f"错误类型: {type(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"设置失败: {str(e)}")

@router.post("/login")
async def login_linkedin(request: LoginRequest):
    """登录LinkedIn"""
    global linkedin_automation, automation_status

    if not linkedin_automation:
        raise HTTPException(status_code=400, detail="请先设置自动化")

    try:
        automation_status["current_task"] = "正在登录LinkedIn..."
        automation_status["is_running"] = True

        # 根据请求中的headless参数重新设置浏览器
        try:
            logger.info(f"Login attempt with headless={request.headless}")
            # 检查是否已有浏览器实例（兼容Selenium和Playwright）
            has_browser = False
            if hasattr(linkedin_automation, 'driver') and linkedin_automation.driver:
                has_browser = True
            elif hasattr(linkedin_automation, 'page') and linkedin_automation.page:
                has_browser = True

            if has_browser:
                try:
                    if automation_status["automation_type"] == "playwright_async":
                        await linkedin_automation.close()
                    elif automation_status["automation_type"] == "playwright":
                        await asyncio.get_event_loop().run_in_executor(executor, linkedin_automation.close)
                    else:
                        linkedin_automation.close()
                    logger.info("Closed existing browser session before new setup.")
                except Exception as e:
                    logger.warning(f"Error closing existing browser: {e}")

            if automation_status["automation_type"] == "playwright_async":
                await linkedin_automation.setup_driver(headless=request.headless)
            elif automation_status["automation_type"] == "playwright":
                def setup_playwright():
                    return linkedin_automation.setup_driver(headless=request.headless)
                await asyncio.get_event_loop().run_in_executor(executor, setup_playwright)
            else:
                linkedin_automation.setup_driver(headless=request.headless)
            logger.info(f"Browser setup with headless={request.headless} before login.")
        except Exception as e:
            logger.error(f"Error re-setting up browser during login: {e}")
            automation_status["is_running"] = False
            automation_status["current_task"] = f"浏览器设置错误: {str(e)}"
            return {"success": False, "status": f"浏览器设置错误: {str(e)}", "requires_action": False}

        if automation_status["automation_type"] == "playwright_async":
            result = await linkedin_automation.login(request.email, request.password)
        elif automation_status["automation_type"] == "playwright":
            # 使用独立线程运行同步 Playwright 登录，避免 asyncio 循环冲突
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as thread_executor:
                future = thread_executor.submit(linkedin_automation.login, request.email, request.password)
                result = future.result(timeout=180)  # 3分钟超时，给二次验证留时间
        else:
            result = linkedin_automation.login(request.email, request.password)

        automation_status["is_logged_in"] = result["success"]
        automation_status["is_running"] = False
        automation_status["current_task"] = result.get("status", result.get("message", "登录完成"))

        # 如果需要用户操作（2FA验证），启动后台监控任务
        if result.get("requires_action", False):
            logger.info("登录需要用户操作，启动后台监控任务...")
            from fastapi import BackgroundTasks
            import asyncio

            # 创建后台任务来监控登录状态
            asyncio.create_task(monitor_login_completion())

        return result

    except Exception as e:
        automation_status["is_running"] = False
        automation_status["current_task"] = f"登录错误: {str(e)}"
        logger.error(f"LinkedIn登录失败: {str(e)}")
        return {"success": False, "status": f"登录失败: {str(e)}", "requires_action": False}

async def monitor_login_completion():
    """后台监控登录完成状态"""
    global linkedin_automation, automation_status

    if not linkedin_automation:
        return

    logger.info("开始监控登录完成状态...")
    max_wait_time = 300  # 最长等待5分钟
    check_interval = 3   # 每3秒检查一次
    elapsed_time = 0

    try:
        while elapsed_time < max_wait_time:
            await asyncio.sleep(check_interval)
            elapsed_time += check_interval

            # 检查登录状态
            try:
                if automation_status["automation_type"] == "playwright_async":
                    result = await linkedin_automation.verify_login_status()
                elif automation_status["automation_type"] == "playwright":
                    # 使用独立线程运行同步验证
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor(max_workers=1) as thread_executor:
                        future = thread_executor.submit(linkedin_automation.verify_login_status)
                        result = future.result(timeout=30)
                else:
                    result = linkedin_automation.verify_login_status()

                if result["success"]:
                    # 登录成功，更新状态
                    automation_status["is_logged_in"] = True
                    automation_status["current_task"] = "登录成功"
                    logger.info("🎉 后台监控检测到登录成功！")
                    break
                else:
                    # 检查是否仍在验证页面
                    if not result.get("requires_action", False):
                        # 如果不再需要用户操作但也没有登录成功，可能是登录失败
                        automation_status["current_task"] = "登录验证失败，请重试"
                        logger.warning("登录验证失败，停止监控")
                        break

                    # 更新当前任务状态
                    automation_status["current_task"] = f"等待用户完成验证... ({elapsed_time}s)"

            except Exception as e:
                logger.error(f"监控登录状态时出错: {str(e)}")
                # 继续监控，不要因为单次错误而停止
                continue

        # 超时处理
        if elapsed_time >= max_wait_time:
            automation_status["current_task"] = "登录验证超时，请重新登录"
            logger.warning("登录监控超时")

    except Exception as e:
        logger.error(f"登录监控任务异常: {str(e)}")
        automation_status["current_task"] = f"登录监控异常: {str(e)}"

@router.post("/search", response_model=List[JobInfo])
async def search_jobs(request: SearchRequest):
    """搜索职位"""
    global linkedin_automation, automation_status
    
    if not linkedin_automation:
        raise HTTPException(status_code=400, detail="请先设置自动化")
    
    if not automation_status["is_logged_in"]:
        raise HTTPException(status_code=401, detail="请先登录LinkedIn")
    
    try:
        automation_status["current_task"] = f"正在搜索职位: {request.keywords}"
        automation_status["is_running"] = True
        
        # 根据自动化类型调用相应的搜索方法
        if automation_status["automation_type"] == "playwright_async":
            jobs = await linkedin_automation.search_jobs(
                keywords=request.keywords,
                location=request.location,
                easy_apply_only=request.easy_apply_only
            )
        elif automation_status["automation_type"] == "playwright":
            # 使用独立线程运行同步 Playwright 搜索
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as thread_executor:
                future = thread_executor.submit(
                    linkedin_automation.search_jobs,
                    keywords=request.keywords,
                    location=request.location,
                    easy_apply_only=request.easy_apply_only
                )
                jobs = future.result(timeout=120)  # 2分钟超时
        else:
            # Selenium 版本
            jobs = linkedin_automation.search_jobs(
                keywords=request.keywords,
                location=request.location,
                easy_apply_only=request.easy_apply_only
            )
        
        automation_status["is_running"] = False
        automation_status["current_task"] = f"找到 {len(jobs)} 个职位"
        automation_status["progress"]["total_found"] = len(jobs)
        
        return [JobInfo(**job) for job in jobs]
        
    except Exception as e:
        automation_status["is_running"] = False
        automation_status["current_task"] = f"搜索错误: {str(e)}"
        logger.error(f"搜索职位失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.post("/apply/{job_id}")
async def apply_single_job(job_id: str, job_info: JobInfo):
    """申请单个职位"""
    global linkedin_automation, automation_status
    
    if not linkedin_automation:
        raise HTTPException(status_code=400, detail="请先设置自动化")
    
    if not automation_status["is_logged_in"]:
        raise HTTPException(status_code=401, detail="请先登录LinkedIn")
    
    try:
        automation_status["current_task"] = f"正在申请职位: {job_info.title}"
        automation_status["is_running"] = True
        
        success = linkedin_automation.apply_to_job(job_info.model_dump())
        
        automation_status["is_running"] = False
        
        if success:
            automation_status["progress"]["total_applied"] += 1
            automation_status["progress"]["successful_applications"].append(job_info.model_dump())
            automation_status["current_task"] = f"成功申请: {job_info.title}"
            return {"success": True, "message": f"成功申请职位: {job_info.title}"}
        else:
            automation_status["progress"]["failed_applications"].append(job_info.model_dump())
            automation_status["current_task"] = f"申请失败: {job_info.title}"
            return {"success": False, "message": f"申请失败: {job_info.title}"}
            
    except Exception as e:
        automation_status["is_running"] = False
        automation_status["current_task"] = f"申请错误: {str(e)}"
        logger.error(f"申请职位失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"申请失败: {str(e)}")

@router.post("/batch-apply")
async def batch_apply_jobs(request: BatchApplyRequest, background_tasks: BackgroundTasks):
    """批量申请职位（后台任务）"""
    global linkedin_automation, automation_status
    
    if not linkedin_automation:
        raise HTTPException(status_code=400, detail="请先设置自动化")
    
    if not automation_status["is_logged_in"]:
        raise HTTPException(status_code=401, detail="请先登录LinkedIn")
    
    if automation_status["is_running"]:
        raise HTTPException(status_code=409, detail="自动化任务正在运行中")
    
    # 重置进度
    automation_status["progress"] = {
        "total_found": 0,
        "total_applied": 0,
        "successful_applications": [],
        "failed_applications": []
    }
    
    # 添加后台任务
    background_tasks.add_task(
        run_batch_apply,
        request.max_applications,
        request.keywords,
        request.location
    )
    
    return {
        "success": True,
        "message": "批量申请任务已启动，请通过状态API查看进度"
    }

async def run_batch_apply(max_applications: int, keywords: List[str], location: str):
    """运行批量申请任务"""
    global linkedin_automation, automation_status
    
    try:
        automation_status["is_running"] = True
        automation_status["current_task"] = "开始批量申请职位"
        
        applied_count = 0
        
        for keyword in keywords:
            if applied_count >= max_applications:
                break
                
            automation_status["current_task"] = f"搜索职位: {keyword}"
            
            # 搜索职位
            jobs = linkedin_automation.search_jobs(
                keywords=keyword,
                location=location,
                easy_apply_only=True
            )
            
            automation_status["progress"]["total_found"] += len(jobs)
            
            # 申请职位
            for job in jobs:
                if applied_count >= max_applications:
                    break
                    
                automation_status["current_task"] = f"申请职位: {job['title']}"
                
                try:
                    success = linkedin_automation.apply_to_job(job)
                    
                    if success:
                        applied_count += 1
                        automation_status["progress"]["total_applied"] += 1
                        automation_status["progress"]["successful_applications"].append(job)
                    else:
                        automation_status["progress"]["failed_applications"].append(job)
                        
                except Exception as e:
                    logger.error(f"申请职位失败 {job['title']}: {str(e)}")
                    automation_status["progress"]["failed_applications"].append(job)
        
        automation_status["is_running"] = False
        automation_status["current_task"] = f"批量申请完成: 成功申请 {applied_count} 个职位"
        
    except Exception as e:
        automation_status["is_running"] = False
        automation_status["current_task"] = f"批量申请错误: {str(e)}"
        logger.error(f"批量申请失败: {str(e)}")

@router.post("/stop")
async def stop_automation():
    """停止自动化任务"""
    global automation_status
    
    automation_status["is_running"] = False
    automation_status["current_task"] = "任务已停止"
    
    return {"success": True, "message": "自动化任务已停止"}

@router.post("/close")
async def close_automation():
    """关闭自动化（关闭浏览器）"""
    global linkedin_automation, automation_status

    try:
        if linkedin_automation:
            if automation_status.get("automation_type") == "playwright_async":
                await linkedin_automation.close()
            elif automation_status.get("automation_type") == "playwright":
                await asyncio.get_event_loop().run_in_executor(executor, linkedin_automation.close)
            else:
                linkedin_automation.close()
            linkedin_automation = None

        automation_status["is_running"] = False
        automation_status["is_logged_in"] = False
        automation_status["current_task"] = "自动化已关闭"

        return {"success": True, "message": "LinkedIn自动化已关闭"}

    except Exception as e:
        logger.error(f"关闭自动化失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"关闭失败: {str(e)}")

@router.post("/verify-login")
async def verify_login_status():
    """手动验证登录状态"""
    global linkedin_automation, automation_status

    if not linkedin_automation:
        return {"success": False, "message": "请先设置自动化"}

    try:
        if automation_status["automation_type"] == "playwright_async":
            result = await linkedin_automation.verify_login_status()
        elif automation_status["automation_type"] == "playwright":
            # 使用独立线程运行同步 Playwright 验证
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as thread_executor:
                future = thread_executor.submit(linkedin_automation.verify_login_status)
                result = future.result(timeout=30)
        else:
            result = linkedin_automation.verify_login_status()

        automation_status["is_logged_in"] = result["success"]
        automation_status["current_task"] = result["status"]
        return result
    except Exception as e:
        logger.error(f"验证登录状态失败: {str(e)}")
        return {"success": False, "message": f"验证失败: {str(e)}"}

@router.post("/logout")
async def logout_linkedin():
    """登出LinkedIn账号，关闭浏览器并重置状态"""
    global linkedin_automation, automation_status
    try:
        if linkedin_automation:
            linkedin_automation.close()
            linkedin_automation = None
        automation_status["is_logged_in"] = False
        automation_status["is_running"] = False
        automation_status["current_task"] = "已登出LinkedIn"
        return {"success": True, "message": "已成功登出LinkedIn"}
    except Exception as e:
        logger.error(f"登出失败: {str(e)}")
        return {"success": False, "message": f"登出失败: {str(e)}"}

@router.get("/progress")
async def get_progress():
    """获取申请进度"""
    return automation_status["progress"]

@router.get("/applied-jobs")
async def get_applied_jobs():
    """获取已申请的职位列表"""
    global linkedin_automation
    
    if linkedin_automation:
        return {
            "applied_jobs": list(linkedin_automation.applied_jobs),
            "count": len(linkedin_automation.applied_jobs)
        }
    else:
        return {"applied_jobs": [], "count": 0}

@router.get("/logs")
async def get_logs(lines: int = 50, level: str = "INFO"):
    """获取日志内容
    
    Args:
        lines: 返回的日志行数，默认50行
        level: 日志级别，可选值：DEBUG, INFO, WARNING, ERROR, CRITICAL，默认INFO
    """
    try:
        # 验证日志级别
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if level not in valid_levels:
            level = "INFO"
            
        # 读取日志文件
        log_lines = []
        if log_file_path.exists():
            with open(log_file_path, "r", encoding="utf-8") as f:
                all_lines = f.readlines()
                
                # 根据级别筛选日志
                filtered_lines = []
                for line in all_lines:
                    if f"| {level}" in line or (level == "DEBUG" and any(f"| {l}" in line for l in valid_levels)):
                        filtered_lines.append(line)
                
                # 获取最后N行
                log_lines = filtered_lines[-lines:] if filtered_lines else []
        
        # 添加时间戳，用于前端轮询更新
        return {
            "timestamp": time.time(),
            "logs": log_lines,
            "total_lines": len(log_lines)
        }
    except Exception as e:
        logger.error(f"获取日志失败: {str(e)}")
        return {
            "timestamp": time.time(),
            "logs": [f"获取日志失败: {str(e)}"],
            "total_lines": 1
        }