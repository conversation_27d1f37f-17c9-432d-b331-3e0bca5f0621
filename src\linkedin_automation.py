"""LinkedIn自动化模块
实现LinkedIn登录验证、搜索职位、筛选Easy Apply职位并进行投递的功能
"""

import time
import random
from typing import List, Dict, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    NoSuchElementException, 
    TimeoutException, 
    ElementClickInterceptedException,
    StaleElementReferenceException
)
from selenium.webdriver.chrome.options import Options
from loguru import logger
import yaml
from pathlib import Path

class LinkedInAutomation:
    """LinkedIn自动化类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化LinkedIn自动化实例
        
        Args:
            config_path: 配置文件路径
        """
        self.driver = None
        self.wait = None
        self.config = self._load_config(config_path)
        self.is_logged_in = False
        self.applied_jobs = set()  # 记录已申请的职位
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件，优先使用用户设置文件"""
        # 首先尝试加载用户设置文件
        user_settings_path = Path(__file__).resolve().parents[1] / "data_folder" / "user_settings.yaml"
        config = {}

        if user_settings_path.exists():
            try:
                with open(user_settings_path, 'r', encoding='utf-8') as f:
                    user_settings = yaml.safe_load(f)
                    if user_settings:
                        config = user_settings
                        logger.info(f"已加载用户设置文件: {user_settings_path}")
            except Exception as e:
                logger.warning(f"加载用户设置文件失败: {str(e)}")

        # 如果指定了配置文件路径，则合并配置
        if config_path and Path(config_path).exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    custom_config = yaml.safe_load(f)
                    if custom_config:
                        # 深度合并配置
                        config = self._merge_configs(config, custom_config)
                        logger.info(f"已合并自定义配置文件: {config_path}")
            except Exception as e:
                logger.warning(f"加载自定义配置文件失败: {str(e)}")

        # 如果没有任何配置，使用默认配置
        if not config:
            config = self._get_default_config()
            logger.info("使用默认配置")

        # 确保必要的配置项存在
        return self._ensure_config_completeness(config)

    def _merge_configs(self, base_config: Dict, override_config: Dict) -> Dict:
        """深度合并两个配置字典"""
        result = base_config.copy()

        for key, value in override_config.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value

        return result

    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'linkedin': {
                'email': '',
                'password': '',
                'search_keywords': ['python developer', 'software engineer'],
                'location': 'United States',
                'experience_level': ['Entry level', 'Associate', 'Mid-Senior level'],
                'job_type': ['Full-time', 'Part-time', 'Contract'],
                'remote_work': ['On-site', 'Remote', 'Hybrid'],
                'automation_type': 'selenium',
                'application': {
                    'max_applications_per_day': 50,
                    'delay_between_applications': [30, 60],  # 秒
                    'auto_answer_questions': True,
                    'default_answers': {
                        'years_experience': '15',
                        'willing_to_relocate': 'Yes',
                        'authorized_to_work': 'Yes',
                        'require_sponsorship': 'No',
                        'expected_salary': 'Negotiable面议',
                        'start_date': 'Immediately立即'
                    }
                }
            },
            'selenium': {
                'headless': False,
                'implicit_wait': 10,
                'page_load_timeout': 30,
                'window_size': [1920, 1080],
                'use_undetected': True
            }
        }

    def _ensure_config_completeness(self, config: Dict) -> Dict:
        """确保配置的完整性，补充缺失的配置项"""
        default_config = self._get_default_config()
        return self._merge_configs(default_config, config)
    
    def setup_driver(self, headless: bool = None, use_undetected: bool = None) -> webdriver.Chrome:
        """设置Chrome WebDriver，优先使用Undetected ChromeDriver"""
        if headless is None:
            # 支持新旧配置格式
            if 'browser' in self.config.get('linkedin', {}):
                headless = self.config['linkedin']['browser'].get('headless', False)
            else:
                headless = self.config.get('selenium', {}).get('headless', False)

        if use_undetected is None:
            # 支持新旧配置格式
            if 'browser' in self.config.get('linkedin', {}):
                use_undetected = self.config['linkedin']['browser'].get('use_undetected', True)
            else:
                use_undetected = self.config.get('selenium', {}).get('use_undetected', True)

        # 尝试使用Undetected ChromeDriver
        if use_undetected:
            try:
                from src.utils.undetected_chrome_utils import UndetectedChromeManager, check_undetected_availability

                if check_undetected_availability():
                    logger.info("使用Undetected ChromeDriver进行反检测优化...")

                    # 创建用户配置目录
                    profile_path = Path.home() / ".linkedin_automation" / "chrome_profile"

                    manager = UndetectedChromeManager()
                    self.driver = manager.create_driver(
                        headless=headless,
                        profile_path=str(profile_path)
                    )

                    # 设置等待和超时
                    self.driver.implicitly_wait(self.config['selenium']['implicit_wait'])
                    self.driver.set_page_load_timeout(self.config['selenium']['page_load_timeout'])

                    self.wait = WebDriverWait(self.driver, 20)
                    self._chrome_manager = manager  # 保存管理器引用

                    logger.info("Undetected ChromeDriver设置成功")
                    return self.driver
                else:
                    logger.warning("Undetected ChromeDriver不可用，回退到标准Selenium")
            except Exception as e:
                logger.warning(f"Undetected ChromeDriver设置失败，回退到标准Selenium: {str(e)}")

        # 回退到标准Selenium设置
        logger.info("使用标准Selenium ChromeDriver...")
        return self._setup_standard_driver(headless)

    def _setup_standard_driver(self, headless: bool) -> webdriver.Chrome:
        """设置标准Selenium ChromeDriver（回退方案）"""
        # 使用优化后的chrome_browser_options函数
        from src.utils.chrome_utils import chrome_browser_options
        chrome_options = chrome_browser_options()

        if headless:
            chrome_options.add_argument('--headless')

        # 窗口大小
        window_size = self.config['selenium']['window_size']
        chrome_options.add_argument(f'--window-size={window_size[0]},{window_size[1]}')

        # 添加额外的反检测设置
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        try:
            # 使用 webdriver_manager 自动管理 ChromeDriver
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service

            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # 设置等待和超时
            self.driver.implicitly_wait(self.config['selenium']['implicit_wait'])
            self.driver.set_page_load_timeout(self.config['selenium']['page_load_timeout'])

            self.wait = WebDriverWait(self.driver, 20)
            
            logger.info("Chrome WebDriver 设置完成")
            return self.driver
        except Exception as e:
            logger.error(f"设置Chrome WebDriver失败: {str(e)}")
            raise RuntimeError(f"设置Chrome WebDriver失败: {str(e)}")
    
    def login(self, email: str = None, password: str = None) -> Dict:
        """登录LinkedIn"""
        try:
            if not email:
                email = self.config['linkedin']['email']
            if not password:
                password = self.config['linkedin']['password']
                
            if not email or not password:
                logger.error("LinkedIn邮箱或密码未配置")
                return {"success": False, "status": "邮箱或密码未配置", "requires_action": False}
            
            logger.info(f"开始登录LinkedIn... 使用邮箱: {email[:3]}****{email[-10:]}")
            try:
                logger.debug("正在访问LinkedIn登录页面...")
                self.driver.get("https://www.linkedin.com/login")
                logger.debug(f"当前URL: {self.driver.current_url}")
            except Exception as e:
                logger.error(f"无法访问LinkedIn登录页面: {str(e)}")
                return {"success": False, "status": "无法访问LinkedIn登录页面，请检查网络连接或代理设置", "requires_action": False}
            
            # 等待登录页面加载
            try:
                logger.debug("等待登录页面元素加载...")
                email_field = self.wait.until(
                    EC.presence_of_element_located((By.ID, "username"))
                )
                logger.debug("登录页面元素加载成功")
            except TimeoutException:
                logger.error("登录页面加载超时")
                logger.debug(f"当前页面标题: {self.driver.title}")
                logger.debug(f"当前页面URL: {self.driver.current_url}")
                return {"success": False, "status": "登录页面加载超时，请检查网络连接速度", "requires_action": False}
            
            # 输入邮箱
            try:
                logger.debug("正在输入邮箱...")
                email_field.clear()
                self._human_type(email_field, email)
                logger.info("邮箱输入完成")
            except Exception as e:
                logger.error(f"邮箱输入失败: {str(e)}")
                return {"success": False, "status": "邮箱输入失败，请检查邮箱格式是否正确", "requires_action": False}
            
            # 输入密码
            try:
                logger.debug("正在输入密码...")
                password_field = self.driver.find_element(By.ID, "password")
                password_field.clear()
                self._human_type(password_field, password)
                logger.info("密码输入完成")
            except Exception as e:
                logger.error(f"密码输入失败: {str(e)}")
                return {"success": False, "status": "密码输入失败，请检查密码格式是否正确", "requires_action": False}
            
            # 点击登录按钮
            try:
                logger.debug("正在点击登录按钮...")
                login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
                login_button.click()
                logger.info("点击登录按钮完成")
            except Exception as e:
                logger.error(f"登录按钮点击失败: {str(e)}")
                return {"success": False, "status": "登录按钮点击失败，请稍后重试", "requires_action": False}
            
            # 等待登录完成，增加等待时间以确保页面加载
            logger.debug("等待登录处理...")
            time.sleep(8)  # 增加等待时间
            
            # 记录当前URL和页面标题，帮助调试
            logger.debug(f"登录后当前URL: {self.driver.current_url}")
            logger.debug(f"登录后页面标题: {self.driver.title}")
            
            # 检查是否需要验证码或其他验证
            if "challenge" in self.driver.current_url or "checkpoint/challenge" in self.driver.current_url:
                logger.warning("检测到验证码验证")
                return {"success": False, "status": "需要完成验证码验证，请在浏览器中完成验证后重试", "requires_action": True}
            
            # 检查是否需要二次验证（checkpoint）
            if "checkpoint" in self.driver.current_url:
                logger.warning("检测到二次验证，等待用户完成验证...")
                max_wait = 120  # 最长等待秒数
                poll_interval = 2  # 检查间隔
                waited = 0
                while waited < max_wait:
                    try:
                        current_url = self.driver.current_url
                        logger.debug(f"二次验证等待中，当前URL: {current_url}")
                        if ("feed" in current_url or "linkedin.com/feed" in current_url or
                            self.driver.find_elements(By.CLASS_NAME, "global-nav") or
                            self.driver.find_elements(By.CLASS_NAME, "feed-identity-module")):
                            logger.info("二次验证后检测到已登录，进入首页")
                            self.is_logged_in = True
                            return {"success": True, "status": "登录成功", "requires_action": False}
                    except Exception as e:
                        logger.warning(f"等待二次验证时页面异常: {str(e)}")
                    time.sleep(poll_interval)
                    waited += poll_interval
                logger.error("二次验证超时，未检测到登录成功")
                return {"success": False, "status": "二次验证超时，请手动检查浏览器中的登录状态", "requires_action": True}
            
            # 检查是否有错误消息
            try:
                error_elements = self.driver.find_elements(By.CLASS_NAME, "alert-content")
                for error_element in error_elements:
                    if error_element.is_displayed():
                        error_text = error_element.text
                        logger.error(f"登录错误: {error_text}")
                        if "密码" in error_text:
                            return {"success": False, "status": "密码错误，请检查密码是否正确", "requires_action": False}
                        elif "找不到" in error_text or "不存在" in error_text:
                            return {"success": False, "status": "账号不存在，请检查邮箱是否正确", "requires_action": False}
                        else:
                            return {"success": False, "status": f"登录错误: {error_text}", "requires_action": False}
            except Exception as e:
                logger.debug(f"检查错误消息时发生异常: {str(e)}")
                pass
            
            # 验证登录状态
            try:
                # 等待页面加载完成
                logger.debug("验证登录状态...")
                try:
                    self.wait.until(
                        EC.presence_of_element_located((By.CLASS_NAME, "global-nav"))
                    )
                    logger.debug("导航栏元素已找到")
                except TimeoutException:
                    logger.debug("未找到导航栏元素，尝试其他元素")
                    # 尝试其他可能的元素
                    try:
                        self.wait.until(
                            EC.presence_of_element_located((By.CLASS_NAME, "feed-identity-module"))
                        )
                        logger.debug("Feed身份模块元素已找到")
                    except TimeoutException:
                        logger.error("无法找到任何登录成功的标志元素")
                        return {"success": False, "status": "登录可能失败，无法找到登录成功的标志元素", "requires_action": False}
                
                # 检查是否在登录页面
                if "login" in self.driver.current_url:
                    logger.error("登录失败：仍在登录页面")
                    return {"success": False, "status": "登录失败，请检查账号密码是否正确", "requires_action": False}
                
                # 检查是否成功加载导航栏
                if not self.driver.find_elements(By.CLASS_NAME, "global-nav") and not self.driver.find_elements(By.CLASS_NAME, "feed-identity-module"):
                    logger.error("登录失败：无法加载导航栏或Feed身份模块")
                    return {"success": False, "status": "登录失败，页面加载异常", "requires_action": False}
                
                self.is_logged_in = True
                logger.info("LinkedIn登录成功")
                return {"success": True, "status": "登录成功", "requires_action": False}
                
            except TimeoutException:
                logger.error("登录超时：无法加载主页")
                return {"success": False, "status": "登录超时，请检查网络连接", "requires_action": False}
            except Exception as e:
                logger.error(f"登录验证失败: {str(e)}")
                return {"success": False, "status": "登录验证失败，请稍后重试", "requires_action": False}
                
        except Exception as e:
            logger.error(f"登录过程发生未知错误: {str(e)}")
            return {"success": False, "status": "登录过程发生未知错误，请稍后重试", "requires_action": False}

    def verify_login_status(self) -> Dict:
        """手动验证登录状态

        Returns:
            包含验证结果的字典
        """
        try:
            logger.info("开始验证登录状态...")

            # 检查当前URL
            current_url = self.driver.current_url
            logger.debug(f"当前URL: {current_url}")

            # 如果在登录页面，说明未登录
            if "login" in current_url:
                logger.info("当前在登录页面，未登录")
                self.is_logged_in = False
                return {"success": False, "status": "未登录", "requires_action": False}

            # 检查是否需要验证
            if "challenge" in current_url or "checkpoint" in current_url:
                logger.warning("检测到需要验证")
                return {"success": False, "status": "需要完成验证，请在浏览器中完成验证", "requires_action": True}

            # 尝试访问LinkedIn主页来验证登录状态
            try:
                logger.debug("访问LinkedIn主页验证登录状态...")
                self.driver.get("https://www.linkedin.com/feed/")
                time.sleep(3)

                # 检查是否被重定向到登录页面
                if "login" in self.driver.current_url:
                    logger.info("被重定向到登录页面，登录已过期")
                    self.is_logged_in = False
                    return {"success": False, "status": "登录已过期，请重新登录", "requires_action": False}

                # 检查是否有导航栏或其他登录标志（增加等待时间）
                try:
                    # 增加等待时间到30秒，等待导航栏加载
                    wait_long = WebDriverWait(self.driver, 30)
                    wait_long.until(
                        EC.any_of(
                            EC.presence_of_element_located((By.CLASS_NAME, "global-nav")),
                            EC.presence_of_element_located((By.CLASS_NAME, "feed-identity-module")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, "[data-control-name='nav.settings']")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".nav-item__profile-member-photo")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".global-nav__me")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, ".feed-identity"))
                        )
                    )

                    logger.info("登录状态验证成功")
                    self.is_logged_in = True
                    return {"success": True, "status": "已登录", "requires_action": False}

                except TimeoutException:
                    logger.warning("无法找到登录标志元素")
                    # 再次检查URL
                    if "feed" in self.driver.current_url or "linkedin.com" in self.driver.current_url:
                        # 如果在LinkedIn域名下但找不到标志元素，可能是页面加载问题
                        logger.info("在LinkedIn域名下，假设已登录")
                        self.is_logged_in = True
                        return {"success": True, "status": "已登录（部分验证）", "requires_action": False}
                    else:
                        logger.error("登录状态验证失败")
                        self.is_logged_in = False
                        return {"success": False, "status": "登录状态验证失败", "requires_action": False}

            except Exception as e:
                logger.error(f"访问LinkedIn主页失败: {str(e)}")
                return {"success": False, "status": "无法访问LinkedIn，请检查网络连接", "requires_action": False}

        except Exception as e:
            logger.error(f"验证登录状态时发生错误: {str(e)}")
            return {"success": False, "status": "验证登录状态时发生错误", "requires_action": False}

    def search_jobs(self, keywords: str = None, location: str = None,
                   easy_apply_only: bool = True) -> List[Dict]:
        """搜索职位"""
        try:
            if not self.is_logged_in:
                logger.error("请先登录LinkedIn")
                return []

            if not keywords:
                keywords = self.config['linkedin']['search_keywords'][0]
            if not location:
                location = self.config['linkedin']['location']

            logger.info(f"搜索职位: {keywords} in {location}")

            # 构建搜索URL
            search_url = f"https://www.linkedin.com/jobs/search/?keywords={keywords}&location={location}"
            if easy_apply_only:
                search_url += "&f_AL=true"  # Easy Apply筛选

            self.driver.get(search_url)
            time.sleep(5)  # 增加等待时间让页面完全加载

            # 滚动页面以加载更多职位
            self._scroll_to_load_jobs()

            # 处理分页导航，获取所有页面的职位
            all_jobs = self._handle_pagination_and_collect_jobs()

            # 保存最终页面HTML快照到 log 文件夹
            log_dir = Path(__file__).parent.parent / "log"
            log_dir.mkdir(parents=True, exist_ok=True)
            html_file_path = log_dir / "linkedin_jobs_full_page.html"
            with open(html_file_path, "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            logger.info(f"页面HTML快照已保存到: {html_file_path}")

            # 如果分页处理获得了职位，直接返回
            if all_jobs:
                logger.info(f"通过分页处理成功提取 {len(all_jobs)} 个职位信息")
                return all_jobs

            # 否则使用LLM解析当前页面职位信息
            jobs = self._parse_jobs_with_llm(self.driver.page_source)

            if not jobs:
                logger.warning("LLM解析未找到职位，尝试传统方法")
                # 回退到传统解析方法
                jobs = self._extract_jobs_traditional()

            logger.info(f"成功提取 {len(jobs)} 个职位信息")
            return jobs

        except Exception as e:
            logger.error(f"搜索职位失败: {str(e)}", exc_info=True)
            # 保存失败时的页面HTML快照
            log_dir = Path(__file__).parent.parent / "log"
            log_dir.mkdir(parents=True, exist_ok=True)
            error_file_path = log_dir / "linkedin_jobs_error_page.html"
            with open(error_file_path, "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            logger.info(f"错误页面快照已保存到: {error_file_path}")
            return []

    def _scroll_to_load_jobs(self):
        """滚动页面以加载更多职位，包括点击"显示更多"按钮"""
        try:
            logger.info("开始滚动页面加载职位...")
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            scroll_attempts = 0
            max_scrolls = 20  # 增加滚动次数

            # 记录职位数量变化
            last_job_count = 0

            while scroll_attempts < max_scrolls:
                # 滚动到页面底部
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(3)  # 增加等待时间

                # 尝试点击"显示更多职位"按钮
                self._click_show_more_jobs_button()

                # 检查职位数量变化
                current_job_count = self._count_job_cards()
                logger.debug(f"当前职位数量: {current_job_count}")

                # 计算新的滚动高度并与上次的滚动高度进行比较
                new_height = self.driver.execute_script("return document.body.scrollHeight")

                # 如果页面高度和职位数量都没有变化，停止滚动
                if new_height == last_height and current_job_count == last_job_count:
                    logger.info(f"页面高度和职位数量都未变化，停止滚动")
                    break

                last_height = new_height
                last_job_count = current_job_count
                scroll_attempts += 1

            logger.info(f"滚动完成，共滚动 {scroll_attempts} 次，最终职位数量: {last_job_count}")
        except Exception as e:
            logger.warning(f"滚动页面时出错: {str(e)}")

    def _click_show_more_jobs_button(self):
        """点击"显示更多职位"按钮 - LinkedIn现在主要使用数字分页，此方法保留作为备用"""
        try:
            # 只保留最常用的选择器，减少尝试时间
            show_more_selectors = [
                ".infinite-scroller__show-more-button",
                "//button[contains(text(), 'See more jobs')]"
            ]

            for selector in show_more_selectors:
                try:
                    if selector.startswith("//"):
                        button = self.driver.find_element(By.XPATH, selector)
                    else:
                        button = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if button and button.is_displayed() and button.is_enabled():
                        logger.info(f"找到并点击显示更多按钮: {selector}")
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                        time.sleep(1)
                        button.click()
                        time.sleep(3)
                        return True
                except Exception:
                    continue

            return False
        except Exception as e:
            logger.debug(f"点击显示更多按钮时出错: {str(e)}")
            return False

    def _count_job_cards(self):
        """统计当前页面的职位卡片数量"""
        try:
            selectors = [
                ".base-search-card",
                "[data-job-id]",
                ".job-card-container",
                ".jobs-search-results__list-item"
            ]

            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        return len(elements)
                except Exception:
                    continue

            return 0
        except Exception as e:
            logger.debug(f"统计职位卡片数量时出错: {str(e)}")
            return 0

    def _handle_pagination_and_collect_jobs(self):
        """处理LinkedIn分页导航并收集所有页面的职位 - 支持多页处理"""
        try:
            all_jobs = []
            current_page = 1
            max_pages = 20  # 增加最大页数，支持更多页面
            consecutive_failures = 0  # 连续失败计数
            max_consecutive_failures = 3  # 最大连续失败次数
            no_new_jobs_count = 0  # 连续无新职位计数
            max_no_new_jobs = 2  # 最大连续无新职位次数

            logger.info("🚀 开始处理LinkedIn分页导航...")
            logger.info(f"📊 配置: 最大页数={max_pages}, 最大连续失败={max_consecutive_failures}")

            while current_page <= max_pages:
                logger.info(f"📄 正在处理第 {current_page} 页...")

                # 等待页面加载完成
                time.sleep(3)

                # 滚动当前页面加载所有职位
                self._scroll_to_load_jobs()

                # 记录处理前的职位数量
                jobs_before = len(all_jobs)

                # 解析当前页面的职位
                page_jobs = self._parse_jobs_with_llm(self.driver.page_source)
                if page_jobs:
                    # 去重添加职位（基于job_id）
                    existing_job_ids = {job.get('job_id') for job in all_jobs}
                    new_jobs = [job for job in page_jobs if job.get('job_id') not in existing_job_ids]
                    all_jobs.extend(new_jobs)

                    # 检查是否真的有新职位
                    if len(new_jobs) > 0:
                        consecutive_failures = 0  # 重置失败计数
                        no_new_jobs_count = 0  # 重置无新职位计数
                        logger.info(f"✅ 第 {current_page} 页新增 {len(new_jobs)} 个职位，总计 {len(all_jobs)} 个")
                    else:
                        no_new_jobs_count += 1
                        logger.warning(f"⚠️ 第 {current_page} 页未找到新职位（连续无新职位 {no_new_jobs_count} 次）")

                        # 如果连续多次没有新职位，可能是重复页面或到了最后
                        if no_new_jobs_count >= max_no_new_jobs:
                            logger.info(f"🔚 连续 {no_new_jobs_count} 次无新职位，可能已到最后或重复页面")
                            break
                else:
                    consecutive_failures += 1
                    logger.warning(f"❌ 第 {current_page} 页未找到职位（连续失败 {consecutive_failures} 次）")

                # 如果连续多次没有找到任何职位，可能页面有问题
                if consecutive_failures >= max_consecutive_failures:
                    logger.info(f"🚨 连续 {consecutive_failures} 次未找到职位，停止分页处理")
                    break

                # 尝试点击下一页 - 积极尝试翻页
                logger.info(f"🔄 尝试从第 {current_page} 页翻到下一页...")
                next_page_success = self._click_next_page()
                if not next_page_success:
                    logger.info("🔚 无法点击下一页，分页处理完成")
                    break

                current_page += 1

                # 额外安全检查：如果页面数超过预期，给出警告但继续
                if current_page > 15:
                    logger.warning(f"⚠️ 已处理 {current_page-1} 页，页数较多，请确认是否正常")

                if current_page > max_pages:
                    logger.warning(f"🛑 已达到最大页数限制 {max_pages}，强制停止")
                    break

            logger.info(f"🎉 分页处理完成！")
            logger.info(f"📊 总结: 处理了 {current_page-1} 页，收集到 {len(all_jobs)} 个职位")
            return all_jobs

        except Exception as e:
            logger.error(f"❌ 处理分页时出错: {str(e)}")
            return []

    def _click_next_page(self):
        """点击下一页按钮 - 修复逻辑，优先尝试点击而不是预判"""
        try:
            logger.info("🔍 开始寻找下一页按钮...")

            # LinkedIn分页按钮选择器 - 针对"1 2 3 ... Next"结构优化
            next_page_selectors = [
                # 标准Next按钮选择器
                "button[aria-label='Next']",
                "//button[@aria-label='Next']",
                "//button[contains(text(), 'Next')]",
                "//button[contains(@aria-label, 'Next')]",

                # LinkedIn特定的分页结构
                ".artdeco-pagination__button--next",
                "//button[contains(@class, 'artdeco-pagination__button--next')]",

                # 分页容器中的Next按钮
                "//div[contains(@class, 'artdeco-pagination')]//button[contains(text(), 'Next')]",
                "//nav[contains(@class, 'artdeco-pagination')]//button[@aria-label='Next']",

                # 更通用的分页Next按钮
                "//li[contains(@class, 'artdeco-pagination__indicator--number')]/following-sibling::li//button",
                "//button[@data-test-pagination-page-btn='next']",

                # 备选方案：通过位置查找Next按钮
                "//button[contains(@class, 'artdeco-pagination__button') and position()=last()]",
                "//div[contains(@class, 'artdeco-pagination')]//button[last()]"
            ]

            found_next_button = False

            for i, selector in enumerate(next_page_selectors, 1):
                try:
                    logger.info(f"🔍 尝试选择器 {i}/{len(next_page_selectors)}: {selector}")

                    if selector.startswith("//"):
                        next_buttons = self.driver.find_elements(By.XPATH, selector)
                    else:
                        next_buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for next_button in next_buttons:
                        if next_button and next_button.is_displayed():
                            found_next_button = True
                            logger.info(f"✅ 找到Next按钮: {selector}")

                            # 检查按钮状态
                            button_class = next_button.get_attribute("class") or ""
                            button_disabled = next_button.get_attribute("disabled")
                            button_aria_disabled = next_button.get_attribute("aria-disabled")

                            logger.info(f"📊 按钮状态检查:")
                            logger.info(f"   - class: {button_class}")
                            logger.info(f"   - disabled: {button_disabled}")
                            logger.info(f"   - aria-disabled: {button_aria_disabled}")
                            logger.info(f"   - is_enabled(): {next_button.is_enabled()}")

                            # 检查各种禁用状态
                            if (button_disabled == "true" or
                                button_aria_disabled == "true" or
                                "disabled" in button_class.lower() or
                                not next_button.is_enabled()):
                                logger.info("❌ Next按钮已禁用，跳过此按钮")
                                continue

                            # 记录当前URL用于验证页面是否真的改变了
                            current_url = self.driver.current_url
                            logger.info(f"📍 当前URL: {current_url}")

                            logger.info(f"🎯 尝试点击Next按钮...")
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", next_button)
                            time.sleep(1)

                            try:
                                next_button.click()
                                logger.info("✅ 标准点击成功")
                            except Exception as e:
                                logger.info(f"⚠️ 标准点击失败，尝试JS点击: {str(e)}")
                                self.driver.execute_script("arguments[0].click();", next_button)
                                logger.info("✅ JS点击成功")

                            time.sleep(4)  # 等待页面加载

                            # 验证页面是否真的改变了
                            new_url = self.driver.current_url
                            logger.info(f"📍 新URL: {new_url}")

                            if current_url == new_url:
                                logger.warning("⚠️ 点击Next按钮后URL未改变，继续尝试其他按钮")
                                continue

                            logger.info("🎉 成功跳转到下一页！")
                            return True

                except Exception as e:
                    logger.debug(f"❌ 选择器 {selector} 失败: {str(e)}")
                    continue

            # 如果找到了Next按钮但都无法点击，说明可能到了最后一页
            if found_next_button:
                logger.info("🔚 找到了Next按钮但都无法点击，可能已到最后一页")
                return False

            # 如果没有找到Next按钮，尝试数字分页
            logger.info("🔢 未找到Next按钮，尝试数字分页...")
            logger.info("💡 这在多页搜索结果中很常见，如 '1 2 3 ... Next' 结构")
            return self._click_numbered_pagination()

        except Exception as e:
            logger.error(f"❌ 点击下一页按钮时出错: {str(e)}")
            return False

    def _is_last_page(self):
        """检测是否已经到了最后一页"""
        try:
            # 方法1: 检查Next按钮是否被禁用
            next_button_selectors = [
                "button[aria-label='Next']",
                "//button[@aria-label='Next']"
            ]

            for selector in next_button_selectors:
                try:
                    if selector.startswith("//"):
                        next_button = self.driver.find_element(By.XPATH, selector)
                    else:
                        next_button = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if next_button:
                        button_class = next_button.get_attribute("class") or ""
                        button_disabled = next_button.get_attribute("disabled")
                        button_aria_disabled = next_button.get_attribute("aria-disabled")

                        if (button_disabled == "true" or
                            button_aria_disabled == "true" or
                            "disabled" in button_class.lower() or
                            not next_button.is_enabled()):
                            logger.info("Next按钮已禁用，确认为最后一页")
                            return True
                except Exception:
                    continue

            # 方法2: 检查分页信息文本
            pagination_info_selectors = [
                ".artdeco-pagination__pages-state",
                "//span[contains(text(), 'of')]",
                "//span[contains(@class, 'pagination')]"
            ]

            for selector in pagination_info_selectors:
                try:
                    if selector.startswith("//"):
                        element = self.driver.find_element(By.XPATH, selector)
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if element:
                        text = element.text.strip()
                        # 检查类似 "Page 2 of 2" 的文本
                        if "of" in text:
                            parts = text.split("of")
                            if len(parts) == 2:
                                try:
                                    current = int(parts[0].strip().split()[-1])
                                    total = int(parts[1].strip())
                                    if current >= total:
                                        logger.info(f"分页信息显示已到最后一页: {text}")
                                        return True
                                except ValueError:
                                    continue
                except Exception:
                    continue

            # 方法3: 检查是否只有一页（没有分页控件）
            pagination_selectors = [
                ".artdeco-pagination",
                "//nav[contains(@class, 'pagination')]",
                "//button[contains(@aria-label, 'Page')]"
            ]

            has_pagination = False
            for selector in pagination_selectors:
                try:
                    if selector.startswith("//"):
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    if elements:
                        has_pagination = True
                        break
                except Exception:
                    continue

            if not has_pagination:
                logger.info("未找到分页控件，可能只有一页")
                return True

            return False

        except Exception as e:
            logger.debug(f"检测最后一页时出错: {str(e)}")
            return False

    def _click_numbered_pagination(self):
        """点击数字分页按钮 - 优化检测逻辑"""
        try:
            logger.info("🔢 开始数字分页检测...")

            # 首先列出所有可见的分页按钮
            page_button_selectors = [
                "//button[contains(@aria-label, 'Page')]",
                "//button[contains(@class, 'artdeco-pagination__button') and not(contains(@aria-label, 'Next')) and not(contains(@aria-label, 'Previous'))]",
                ".artdeco-pagination button:not([aria-label*='Next']):not([aria-label*='Previous'])"
            ]

            all_page_buttons = []
            for selector in page_button_selectors:
                try:
                    if selector.startswith("//"):
                        buttons = self.driver.find_elements(By.XPATH, selector)
                    else:
                        buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    all_page_buttons.extend(buttons)
                except Exception:
                    continue

            # 打印所有找到的分页按钮信息
            logger.info(f"📋 找到 {len(all_page_buttons)} 个分页按钮:")
            for i, btn in enumerate(all_page_buttons):
                try:
                    btn_text = btn.text.strip()
                    btn_aria = btn.get_attribute('aria-label') or ''
                    btn_class = btn.get_attribute('class') or ''
                    logger.info(f"   按钮{i+1}: 文本='{btn_text}', aria-label='{btn_aria}', class='{btn_class}'")
                except:
                    pass

            if not all_page_buttons:
                logger.info("❌ 未找到任何数字分页按钮")
                return False

            # 找到当前激活的页码 - 扩展检测方法
            current_page_num = None
            current_page_selectors = [
                "//button[contains(@class, 'selected') or contains(@class, 'active') or @aria-current='true']",
                "//button[contains(@class, 'artdeco-pagination__button--active')]",
                "button[aria-current='true']",
                ".artdeco-pagination__button--active"
            ]

            for selector in current_page_selectors:
                try:
                    if selector.startswith("//"):
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for element in elements:
                        if element and element.text.strip().isdigit():
                            current_page_num = int(element.text.strip())
                            logger.info(f"✅ 检测到当前页码: {current_page_num}")
                            break

                    if current_page_num:
                        break
                except Exception:
                    continue

            # 如果找不到当前页码，尝试从URL中获取
            if current_page_num is None:
                try:
                    current_url = self.driver.current_url
                    logger.info(f"📍 当前URL: {current_url}")

                    if "start=" in current_url:
                        start_param = current_url.split("start=")[1].split("&")[0]
                        start_value = int(start_param)
                        current_page_num = (start_value // 25) + 1  # LinkedIn每页25个职位
                        logger.info(f"📊 从URL获取当前页码: {current_page_num} (start={start_value})")
                    else:
                        # 如果URL中没有start参数，假设是第1页
                        current_page_num = 1
                        logger.info("📊 URL中无start参数，假设为第1页")
                except Exception as e:
                    logger.info(f"⚠️ 从URL获取页码失败: {str(e)}")
                    current_page_num = 1

            if current_page_num:
                next_page_num = current_page_num + 1
                logger.info(f"🎯 当前页码: {current_page_num}，尝试点击页码: {next_page_num}")

                # 检查下一页按钮是否存在 - 扩展选择器
                next_page_button_selectors = [
                    f"//button[@aria-label='Page {next_page_num}']",
                    f"//button[text()='{next_page_num}']",
                    f"//button[contains(text(), '{next_page_num}') and contains(@class, 'artdeco-pagination__button')]",
                    f"//button[normalize-space(text())='{next_page_num}']"
                ]

                for selector in next_page_button_selectors:
                    try:
                        next_buttons = self.driver.find_elements(By.XPATH, selector)
                        for next_button in next_buttons:
                            if next_button and next_button.is_displayed() and next_button.is_enabled():
                                # 记录当前URL用于验证
                                current_url = self.driver.current_url

                                logger.info(f"✅ 找到页码 {next_page_num} 按钮，准备点击")
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", next_button)
                                time.sleep(1)

                                try:
                                    next_button.click()
                                    logger.info("✅ 标准点击成功")
                                except Exception:
                                    self.driver.execute_script("arguments[0].click();", next_button)
                                    logger.info("✅ JS点击成功")

                                time.sleep(4)  # 等待页面加载

                                # 验证页面是否真的改变了
                                new_url = self.driver.current_url
                                logger.info(f"📍 新URL: {new_url}")

                                if current_url == new_url:
                                    logger.warning("⚠️ 点击页码按钮后URL未改变，继续尝试其他按钮")
                                    continue

                                logger.info(f"🎉 成功跳转到第 {next_page_num} 页！")
                                return True
                    except Exception as e:
                        logger.debug(f"❌ 尝试点击页码 {next_page_num} 失败: {str(e)}")
                        continue

            logger.info("🔚 未找到下一页按钮，可能已到最后一页")
            return False

        except Exception as e:
            logger.error(f"❌ 点击数字分页按钮时出错: {str(e)}")
            return False

    def _parse_jobs_with_llm(self, page_source: str) -> List[Dict]:
        """使用LLM解析职位信息"""
        try:
            logger.info("开始使用LLM解析职位信息...")

            # 获取API密钥
            api_key = self._get_api_key()
            if not api_key:
                logger.error("未找到API密钥，无法使用LLM解析")
                return []

            # 导入LLM相关模块
            from langchain_google_genai import ChatGoogleGenerativeAI
            from langchain_core.prompts import ChatPromptTemplate
            from langchain_core.output_parsers import StrOutputParser
            import json

            # 初始化LLM
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.5-flash-preview-05-20",
                google_api_key=api_key,
                temperature=1.0,  # 使用用户偏好的温度设置
                request_timeout=60.0,
                max_retries=3,
                transport="rest"
            )

            # 创建简化的解析提示（大幅减少token使用）
            prompt_template = ChatPromptTemplate.from_template("""
从HTML提取职位信息，只返回JSON数组，不要包含代码或解释。

HTML: {html_content}

只返回JSON格式：[{{"title":"标题","company":"公司","location":"地点","url":"链接","is_easy_apply":true,"job_id":"ID"}}]
""")

            # 截取HTML内容的关键部分以避免token限制
            truncated_html = self._truncate_html_for_llm(page_source)

            # 创建链式处理
            chain = prompt_template | llm | StrOutputParser()

            # 调用LLM（带重试机制）
            logger.info("正在调用LLM解析职位信息...")
            response = self._call_llm_with_retry(chain, {"html_content": truncated_html})

            # 添加调试信息
            logger.info(f"LLM原始响应长度: {len(response)} 字符")
            logger.info(f"LLM响应前500字符: {response[:500]}")

            # 解析JSON响应
            try:
                # 清理响应文本，移除可能的markdown标记和代码块
                clean_response = response.strip()

                # 移除各种代码块标记
                if clean_response.startswith("```python"):
                    clean_response = clean_response[9:]
                elif clean_response.startswith("```json"):
                    clean_response = clean_response[7:]
                elif clean_response.startswith("```"):
                    clean_response = clean_response[3:]

                if clean_response.endswith("```"):
                    clean_response = clean_response[:-3]

                # 如果响应包含Python代码，尝试提取JSON部分
                import re
                json_match = re.search(r'\[.*?\]', clean_response, re.DOTALL)
                if json_match:
                    clean_response = json_match.group()
                else:
                    # 如果没有找到JSON数组，查找单个JSON对象
                    json_match = re.search(r'\{.*?\}', clean_response, re.DOTALL)
                    if json_match:
                        clean_response = '[' + json_match.group() + ']'

                clean_response = clean_response.strip()
                logger.info(f"清理后的响应前500字符: {clean_response[:500]}")

                jobs_data = json.loads(clean_response)

                # 验证和清理数据
                validated_jobs = []
                seen_job_ids = set()

                for job in jobs_data:
                    if isinstance(job, dict) and job.get('title') and job.get('company'):
                        # 确保job_id存在
                        if not job.get('job_id'):
                            job['job_id'] = self._extract_job_id(job.get('url', '')) or str(hash(job.get('title', '') + job.get('company', '')))

                        # 确保URL是完整的LinkedIn链接
                        if job.get('url') and not job['url'].startswith('http'):
                            if job['url'].startswith('/'):
                                job['url'] = 'https://www.linkedin.com' + job['url']

                        # 基于job_id去重
                        job_id = job.get('job_id')
                        if job_id and job_id not in seen_job_ids:
                            seen_job_ids.add(job_id)
                            validated_jobs.append(job)
                        elif not job_id:
                            # 如果没有job_id，仍然添加（可能是解析错误）
                            validated_jobs.append(job)

                logger.info(f"LLM成功解析出 {len(validated_jobs)} 个唯一职位")
                return validated_jobs

            except json.JSONDecodeError as e:
                logger.error(f"LLM响应JSON解析失败: {str(e)}")
                logger.debug(f"LLM原始响应: {response[:500]}...")
                return []

        except Exception as e:
            logger.error(f"LLM解析职位信息失败: {str(e)}")
            return []

    def _call_llm_with_retry(self, chain, input_data: dict, max_retries: int = 3) -> str:
        """带重试机制的LLM调用，处理配额限制"""
        import time

        for attempt in range(max_retries):
            try:
                logger.info(f"LLM调用尝试 {attempt + 1}/{max_retries}")
                response = chain.invoke(input_data)
                logger.info("LLM调用成功")
                return response

            except Exception as e:
                error_str = str(e)
                logger.warning(f"LLM调用失败 (尝试 {attempt + 1}/{max_retries}): {error_str}")

                # 检查是否是配额限制错误
                if "429" in error_str or "quota" in error_str.lower() or "rate limit" in error_str.lower():
                    if attempt < max_retries - 1:
                        # 从错误信息中提取重试延迟时间
                        retry_delay = 30  # 默认30秒
                        if "retryDelay" in error_str:
                            try:
                                import re
                                delay_match = re.search(r"retryDelay.*?(\d+)s", error_str)
                                if delay_match:
                                    retry_delay = int(delay_match.group(1))
                            except:
                                pass

                        logger.info(f"检测到配额限制，等待 {retry_delay} 秒后重试...")
                        time.sleep(retry_delay)
                        continue
                    else:
                        logger.error("达到最大重试次数，配额限制无法解决")
                        raise e
                else:
                    # 其他错误，直接抛出
                    raise e

        raise Exception("LLM调用失败，已达到最大重试次数")

    def _get_api_key(self) -> str:
        """获取API密钥"""
        try:
            # 尝试从多个来源获取API密钥
            import os
            from pathlib import Path
            import yaml

            # 1. 从环境变量获取
            api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
            if api_key:
                return api_key

            # 2. 从secrets.yaml文件获取
            secrets_file = Path(__file__).parent.parent / "secrets.yaml"
            if secrets_file.exists():
                with open(secrets_file, 'r', encoding='utf-8') as f:
                    secrets = yaml.safe_load(f)
                    api_key = secrets.get('gemini_api_key') or secrets.get('google_api_key') or secrets.get('llm_api_key')
                    if api_key:
                        return api_key

            # 3. 从data_folder/secrets.yaml获取
            data_secrets_file = Path(__file__).parent.parent / "data_folder" / "secrets.yaml"
            if data_secrets_file.exists():
                with open(data_secrets_file, 'r', encoding='utf-8') as f:
                    secrets = yaml.safe_load(f)
                    api_key = secrets.get('gemini_api_key') or secrets.get('google_api_key') or secrets.get('llm_api_key')
                    if api_key:
                        return api_key

            logger.warning("未找到API密钥")
            return None

        except Exception as e:
            logger.error(f"获取API密钥时出错: {str(e)}")
            return None

    def _truncate_html_for_llm(self, html_content: str, max_chars: int = 100000) -> str:
        """智能截取和清理HTML内容以适应LLM token限制和API配额"""
        logger.info(f"开始处理HTML内容，原始长度: {len(html_content)} 字符")

        # 第一步：智能提取职位相关的HTML片段
        job_sections = self._extract_job_sections(html_content)

        if job_sections:
            # 保持所有职位片段，不限制数量
            combined_content = '\n'.join(job_sections)
            logger.info(f"提取到 {len(job_sections)} 个职位片段，合并后长度: {len(combined_content)} 字符")
        else:
            # 如果没有找到职位片段，使用原始内容
            combined_content = html_content
            logger.warning("未找到职位片段，使用原始HTML内容")

        # 第二步：清理HTML内容，移除不必要的元素
        cleaned_content = self._clean_html_for_llm(combined_content)
        logger.info(f"HTML清理后长度: {len(cleaned_content)} 字符")

        # 第三步：如果仍然超过限制，进行智能截取
        if len(cleaned_content) <= max_chars:
            return cleaned_content

        # 查找包含最多职位关键词的部分
        job_keywords = ['job', 'position', 'career', 'work', 'employment', 'base-search-card', 'job-card', 'easy-apply']
        best_start = 0
        best_score = 0

        for i in range(0, len(cleaned_content) - max_chars, 2000):
            chunk = cleaned_content[i:i + max_chars].lower()
            score = sum(chunk.count(keyword) for keyword in job_keywords)
            if score > best_score:
                best_score = score
                best_start = i

        truncated = cleaned_content[best_start:best_start + max_chars]
        logger.info(f"HTML内容最终截取: {len(cleaned_content)} -> {len(truncated)} 字符")
        return truncated

    def _extract_job_sections(self, html_content: str) -> List[str]:
        """提取HTML中的职位相关片段"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            # 定义职位卡片的可能选择器（按优先级排序）
            job_selectors = [
                '[data-job-id]',  # 最准确的职位标识
                '.base-search-card',
                '.job-card-container',
                '.jobs-search-results__list-item',
                '.job-card-list__item',
                '.job-search-card',
                '.jobs-search-card',
                '.artdeco-entity-lockup'
            ]

            job_sections = []
            found_selector = None

            for selector in job_selectors:
                elements = soup.select(selector)
                if elements:
                    logger.info(f"使用选择器 {selector} 找到 {len(elements)} 个职位元素")
                    found_selector = selector

                    for i, element in enumerate(elements):
                        job_html = str(element)
                        # 验证职位片段质量
                        if self._validate_job_section(job_html):
                            job_sections.append(job_html)
                            logger.debug(f"添加职位片段 {i+1}: {len(job_html)} 字符")
                        else:
                            logger.debug(f"跳过低质量职位片段 {i+1}")
                    break  # 找到职位元素后就停止

            # 如果没有找到足够的职位卡片，尝试其他方法
            if len(job_sections) < 5:
                logger.info(f"当前只找到 {len(job_sections)} 个职位片段，尝试其他方法")
                additional_sections = self._extract_additional_job_sections(soup)
                job_sections.extend(additional_sections)

            # 去重（基于内容相似性）
            unique_sections = self._deduplicate_job_sections(job_sections)

            logger.info(f"总共提取到 {len(unique_sections)} 个有效职位片段")
            return unique_sections[:30]  # 恢复到30个片段

        except Exception as e:
            logger.warning(f"提取职位片段失败: {str(e)}")
            return []

    def _validate_job_section(self, job_html: str) -> bool:
        """验证职位片段的质量"""
        if len(job_html) < 50:  # 降低最小长度要求
            return False

        # 检查是否包含职位相关的关键信息
        job_html_lower = job_html.lower()
        # 扩展关键词列表，降低要求
        required_keywords = ['job', 'position', 'company', 'location', 'title', 'apply', 'hiring', 'work', 'career', 'role']
        found_keywords = sum(1 for keyword in required_keywords if keyword in job_html_lower)

        # 降低要求：至少包含1个关键词即可
        return found_keywords >= 1

    def _extract_additional_job_sections(self, soup) -> List[str]:
        """提取额外的职位片段"""
        additional_sections = []

        # 查找包含职位关键词的div
        all_divs = soup.find_all(['div', 'article', 'section'])
        for div in all_divs:
            div_text = div.get_text().lower()
            if any(keyword in div_text for keyword in ['easy apply', 'apply now', 'hiring', 'position']):
                job_html = str(div)
                if self._validate_job_section(job_html):
                    additional_sections.append(job_html)
                    if len(additional_sections) >= 10:  # 限制额外片段数量
                        break

        logger.info(f"额外找到 {len(additional_sections)} 个职位片段")
        return additional_sections

    def _deduplicate_job_sections(self, job_sections: List[str]) -> List[str]:
        """去除重复的职位片段"""
        unique_sections = []
        seen_hashes = set()

        for section in job_sections:
            # 使用内容的hash来检测重复
            section_hash = hash(section[:500])  # 使用前500字符计算hash
            if section_hash not in seen_hashes:
                seen_hashes.add(section_hash)
                unique_sections.append(section)

        logger.info(f"去重后保留 {len(unique_sections)} 个唯一职位片段")
        return unique_sections

    def _clean_html_for_llm(self, html_content: str) -> str:
        """激进清理HTML内容，移除不必要的元素以大幅减少token使用"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')

            # 移除所有不必要的标签
            tags_to_remove = ['script', 'style', 'noscript', 'iframe', 'embed', 'object', 'svg', 'img',
                             'nav', 'header', 'footer', 'aside', 'form', 'input', 'button', 'select', 'textarea']
            for tag in tags_to_remove:
                for element in soup.find_all(tag):
                    element.decompose()

            # 移除所有属性，只保留最关键的
            for element in soup.find_all():
                attrs_to_keep = ['data-job-id', 'href']  # 只保留最关键的属性
                attrs_to_remove = [attr for attr in element.attrs if attr not in attrs_to_keep]
                for attr in attrs_to_remove:
                    del element[attr]

            # 移除空元素和只包含空白的元素
            for element in soup.find_all():
                if not element.get_text(strip=True) and not element.find_all():
                    element.decompose()

            # 转换为文本并进行激进清理
            cleaned_html = str(soup)
            import re

            # 移除多余的HTML标签，只保留基本结构
            cleaned_html = re.sub(r'<(?!/?(?:div|span|a|p|h[1-6]|ul|li|strong|em)\b)[^>]*>', '', cleaned_html)

            # 合并多个空白字符
            cleaned_html = re.sub(r'\s+', ' ', cleaned_html)

            # 移除标签间的空白
            cleaned_html = re.sub(r'>\s+<', '><', cleaned_html)

            # 移除空行
            cleaned_html = re.sub(r'\n\s*\n', '\n', cleaned_html)

            return cleaned_html.strip()

        except Exception as e:
            logger.warning(f"HTML清理失败: {str(e)}")
            # 如果清理失败，至少进行基本的文本清理
            import re
            cleaned = re.sub(r'<[^>]+>', ' ', html_content)  # 移除所有HTML标签
            cleaned = re.sub(r'\s+', ' ', cleaned)  # 合并空白
            return cleaned.strip()[:30000]  # 限制长度

    def _extract_jobs_traditional(self) -> List[Dict]:
        """传统方法提取职位信息（作为LLM解析的备用方案）"""
        try:
            logger.info("使用传统方法提取职位信息...")

            # 尝试多种选择器
            selectors = [
                ".base-search-card",
                ".job-card-container",
                ".jobs-search-results__list-item",
                "[data-job-id]",
                ".job-card-list__item"
            ]

            job_cards = []
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        job_cards = elements
                        logger.info(f"使用选择器 {selector} 找到 {len(job_cards)} 个职位卡片")
                        break
                except Exception:
                    continue

            if not job_cards:
                logger.warning("未找到任何职位卡片")
                return []

            jobs = []
            max_jobs = min(len(job_cards), 100)  # 增加到最多100个职位
            logger.info(f"开始提取 {max_jobs} 个职位信息...")

            for i, card in enumerate(job_cards[:max_jobs]):
                try:
                    job_info = self._extract_job_info(card)
                    if job_info:
                        jobs.append(job_info)
                        if (i + 1) % 10 == 0:  # 每10个职位记录一次进度
                            logger.info(f"已提取 {len(jobs)} 个有效职位 (处理了 {i + 1}/{max_jobs} 个卡片)")
                except Exception as e:
                    logger.warning(f"提取第 {i+1} 个职位信息失败: {str(e)}")
                    continue

            logger.info(f"传统方法成功提取 {len(jobs)} 个职位")
            return jobs

        except Exception as e:
            logger.error(f"传统方法提取职位失败: {str(e)}")
            return []

    def _extract_job_info(self, job_card) -> Optional[Dict]:
        """从职位卡片提取信息"""
        try:
            # 捕获职位卡片的HTML内容
            logger.debug(f"职位卡片HTML: {job_card.get_attribute('outerHTML')}")

            # 职位标题
            title_element = job_card.find_element(
                By.CSS_SELECTOR, 
                ".job-card-list__title, .jobs-unified-top-card__job-title a, h3 a"
            )
            title = title_element.text.strip()
            job_url = title_element.get_attribute('href')

            # 公司名称
            company_element = job_card.find_element(
                By.CSS_SELECTOR,
                ".job-card-container__company-name, .jobs-unified-top-card__company-name a, .job-card-container__primary-description"
            )
            company = company_element.text.strip()

            # 地点
            try:
                location_element = job_card.find_element(
                    By.CSS_SELECTOR,
                    ".job-card-container__metadata-item, .jobs-unified-top-card__bullet"
                )
                location = location_element.text.strip()
            except:
                location = "未知地点"

            # 检查是否为Easy Apply - 使用多种检测方法
            is_easy_apply = False

            # 方法1: 检查卡片HTML内容
            try:
                card_html = job_card.get_attribute('outerHTML')
                easy_apply_indicators = [
                    "Easy Apply", "轻松申请", "抢先申请", "正在招聘",
                    "job-posting-benefits", "Apply now", "easy-apply"
                ]
                for indicator in easy_apply_indicators:
                    if indicator in card_html:
                        is_easy_apply = True
                        break
            except:
                pass

            # 方法2: 查找Easy Apply相关元素
            if not is_easy_apply:
                easy_apply_selectors = [
                    ".jobs-apply-button--top-card",
                    ".job-card-container__apply-method",
                    ".job-posting-benefits",
                    ".jobs-s-apply",
                    "[data-control-name*='easy_apply']",
                    "[data-control-name*='job_card_easy_apply']",
                    ".easy-apply-button",
                    "button[aria-label*='Easy Apply']"
                ]

                for selector in easy_apply_selectors:
                    try:
                        easy_apply_element = job_card.find_element(By.CSS_SELECTOR, selector)
                        if easy_apply_element and easy_apply_element.is_displayed():
                            element_text = easy_apply_element.text
                            if ("Easy Apply" in element_text or "轻松申请" in element_text or
                                "抢先申请" in element_text or element_text == ""):
                                is_easy_apply = True
                                break
                    except:
                        continue

            # 方法3: 查找包含Easy Apply文本的任何元素
            if not is_easy_apply:
                try:
                    easy_apply_text_elements = job_card.find_elements(
                        By.XPATH,
                        ".//*[contains(text(), 'Easy Apply') or contains(text(), '轻松申请') or contains(text(), '抢先申请')]"
                    )
                    if easy_apply_text_elements:
                        is_easy_apply = True
                except:
                    pass

            return {
                'title': title,
                'company': company,
                'location': location,
                'url': job_url,
                'is_easy_apply': is_easy_apply,
                'job_id': self._extract_job_id(job_url)
            }

        except Exception as e:
            logger.warning(f"提取职位信息失败: {str(e)}")
            return None
    
    def _extract_job_id(self, job_url: str) -> str:
        """从URL提取职位ID"""
        try:
            if '/jobs/view/' in job_url:
                return job_url.split('/jobs/view/')[1].split('/')[0]
            return job_url.split('/')[-1].split('?')[0]
        except:
            return str(hash(job_url))
    
    def apply_to_job(self, job_info: Dict) -> bool:
        """申请职位"""
        try:
            job_id = job_info['job_id']
            
            # 检查是否已申请
            if job_id in self.applied_jobs:
                logger.info(f"职位 {job_info['title']} 已申请过，跳过")
                return False
            
            if not job_info['is_easy_apply']:
                logger.info(f"职位 {job_info['title']} 不支持Easy Apply，跳过")
                return False
            
            logger.info(f"开始申请职位: {job_info['title']} at {job_info['company']}")
            logger.info(f"职位URL: {job_info['url']}")
            logger.info(f"Easy Apply状态: {job_info['is_easy_apply']}")

            # 打开职位页面
            self.driver.get(job_info['url'])
            time.sleep(3)  # 增加等待时间确保页面完全加载

            # 等待页面加载完成
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                logger.info("职位页面加载完成")
            except TimeoutException:
                logger.warning("职位页面加载超时")
                return False
            
            # 查找并点击Easy Apply按钮 - 使用多种选择器策略
            easy_apply_button = None
            easy_apply_selectors = [
                # LinkedIn 2024年最新的Easy Apply按钮选择器
                "//button[contains(@class, 'jobs-apply-button') and contains(., 'Easy Apply')]",
                "//button[contains(@aria-label, 'Easy Apply')]",
                "//button[contains(text(), 'Easy Apply')]",
                "//button[contains(@class, 'artdeco-button--primary') and contains(., 'Easy Apply')]",
                "//button[@data-control-name='jobdetails_topcard_inapply']",
                "//button[contains(@class, 'jobs-s-apply') and contains(., 'Easy Apply')]",
                "//button[contains(@class, 'jobs-apply-button--top-card')]",
                # 2024年新增选择器
                "//button[contains(@class, 'jobs-apply-button') and contains(@class, 'artdeco-button--primary')]",
                "//button[@data-test-id='jobs-apply-button']",
                "//button[contains(@class, 'jobs-apply-button--top-card') and contains(., 'Easy Apply')]",
                "//button[contains(@class, 'jobs-apply-button') and @aria-label]",
                "//button[contains(@class, 'jobs-apply-button') and contains(@class, 'artdeco-button')]",
                # 更精确的选择器
                "//button[normalize-space(text())='Easy Apply']",
                "//button[@aria-label='Easy Apply to this job']",
                "//button[contains(@class, 'jobs-apply-button') and not(contains(@class, 'disabled'))]",
                # 中文版本
                "//button[contains(., '轻松申请')]",
                "//button[contains(., '抢先申请')]",
                # 通用Easy Apply按钮
                "//*[contains(@class, 'easy-apply') or contains(@data-control-name, 'easy_apply')]//button",
                "//button[contains(@class, 'apply-button')]",
                # 备用选择器
                "//button[contains(@data-control-name, 'apply')]",
                "//button[contains(@class, 'artdeco-button--primary') and contains(text(), 'Apply')]"
            ]

            try:
                for selector in easy_apply_selectors:
                    try:
                        logger.info(f"尝试选择器: {selector}")
                        easy_apply_button = self.wait.until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        if easy_apply_button:
                            logger.info(f"找到Easy Apply按钮，使用选择器: {selector}")
                            break
                    except TimeoutException:
                        continue

                if not easy_apply_button:
                    # 最后尝试：查找所有包含"Easy Apply"文本的按钮
                    logger.info("尝试查找所有包含Easy Apply文本的按钮...")
                    all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    for button in all_buttons:
                        if button.is_displayed() and button.is_enabled():
                            button_text = button.text.strip()
                            if "Easy Apply" in button_text or "轻松申请" in button_text:
                                easy_apply_button = button
                                logger.info(f"通过文本匹配找到Easy Apply按钮: {button_text}")
                                break

                if easy_apply_button:
                    # 记录按钮信息
                    button_text = easy_apply_button.text or easy_apply_button.get_attribute('aria-label') or 'Easy Apply'
                    button_class = easy_apply_button.get_attribute('class')
                    logger.info(f"找到Easy Apply按钮: '{button_text}' (class: {button_class})")

                    # 确保按钮可见和可点击
                    if not easy_apply_button.is_displayed():
                        logger.warning("Easy Apply按钮不可见，尝试滚动到按钮位置")
                        self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", easy_apply_button)
                        time.sleep(2)

                    if not easy_apply_button.is_enabled():
                        logger.error("Easy Apply按钮不可点击")
                        return False

                    # 多种点击方法尝试
                    click_success = False

                    # 方法1: 标准点击
                    try:
                        logger.info("尝试方法1: 标准点击")
                        easy_apply_button.click()
                        click_success = True
                        logger.info("方法1成功: 标准点击")
                    except Exception as e:
                        logger.warning(f"方法1失败: {str(e)}")

                    # 方法2: JavaScript点击
                    if not click_success:
                        try:
                            logger.info("尝试方法2: JavaScript点击")
                            self.driver.execute_script("arguments[0].click();", easy_apply_button)
                            click_success = True
                            logger.info("方法2成功: JavaScript点击")
                        except Exception as e:
                            logger.warning(f"方法2失败: {str(e)}")

                    # 方法3: ActionChains点击
                    if not click_success:
                        try:
                            from selenium.webdriver.common.action_chains import ActionChains
                            logger.info("尝试方法3: ActionChains点击")
                            actions = ActionChains(self.driver)
                            actions.move_to_element(easy_apply_button).click().perform()
                            click_success = True
                            logger.info("方法3成功: ActionChains点击")
                        except Exception as e:
                            logger.warning(f"方法3失败: {str(e)}")

                    # 方法4: 强制JavaScript点击
                    if not click_success:
                        try:
                            logger.info("尝试方法4: 强制JavaScript点击")
                            self.driver.execute_script("""
                                arguments[0].style.display = 'block';
                                arguments[0].style.visibility = 'visible';
                                arguments[0].disabled = false;
                                arguments[0].click();
                            """, easy_apply_button)
                            click_success = True
                            logger.info("方法4成功: 强制JavaScript点击")
                        except Exception as e:
                            logger.warning(f"方法4失败: {str(e)}")

                    if not click_success:
                        logger.error("所有点击方法都失败了")
                        return False

                    logger.info("Easy Apply按钮点击成功！")
                    time.sleep(3)  # 等待页面响应

                    # 检查页面是否发生变化
                    current_url = self.driver.current_url
                    logger.info(f"点击后当前URL: {current_url}")

                    # 等待申请弹窗或新页面加载
                    try:
                        # 等待申请相关元素出现
                        WebDriverWait(self.driver, 10).until(
                            EC.any_of(
                                EC.presence_of_element_located((By.CSS_SELECTOR, "[data-test-modal]")),
                                EC.presence_of_element_located((By.CSS_SELECTOR, ".jobs-easy-apply-modal")),
                                EC.presence_of_element_located((By.CSS_SELECTOR, ".artdeco-modal")),
                                EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Next') or contains(text(), 'Submit') or contains(text(), 'Review')]"))
                            )
                        )
                        logger.info("申请弹窗或流程已加载")
                    except TimeoutException:
                        logger.warning("未检测到申请弹窗，可能页面加载较慢")
                        # 截图用于调试
                        try:
                            self.driver.save_screenshot(f"log/after_easy_apply_click_{int(time.time())}.png")
                            logger.info("已保存点击后的页面截图")
                        except Exception as e:
                            logger.warning(f"保存截图失败: {str(e)}")
                else:
                    logger.warning(f"未找到Easy Apply按钮: {job_info['title']}")
                    # 截图用于调试
                    try:
                        self.driver.save_screenshot(f"log/no_easy_apply_{int(time.time())}.png")
                        logger.info("已保存调试截图")
                    except Exception as e:
                        logger.warning(f"保存截图失败: {str(e)}")
                    return False

            except Exception as e:
                logger.error(f"点击Easy Apply按钮失败: {str(e)}")
                return False
            
            # 处理申请流程
            success = self._handle_application_process()
            
            if success:
                self.applied_jobs.add(job_id)
                logger.info(f"成功申请职位: {job_info['title']}")
                
                # 随机延迟
                delay = random.randint(*self.config['linkedin']['delay_between_applications'])
                logger.info(f"等待 {delay} 秒后继续...")
                time.sleep(delay)
                
            return success
            
        except Exception as e:
            logger.error(f"申请职位失败 {job_info['title']}: {str(e)}")
            return False
    
    def _handle_application_process(self) -> bool:
        """处理申请流程"""
        try:
            max_steps = 10  # 增加最大步骤数
            current_step = 0

            logger.info("开始处理LinkedIn申请流程...")

            # 记录当前页面状态
            logger.info(f"申请流程开始时URL: {self.driver.current_url}")
            logger.info(f"申请流程开始时页面标题: {self.driver.title}")

            while current_step < max_steps:
                logger.info(f"申请流程步骤 {current_step + 1}/{max_steps}")
                time.sleep(3)  # 增加等待时间

                # 记录当前页面状态
                logger.debug(f"步骤 {current_step + 1} - 当前URL: {self.driver.current_url}")

                # 检查是否有模态框或弹窗
                modals = self.driver.find_elements(By.CSS_SELECTOR, ".artdeco-modal, .jobs-easy-apply-modal, [data-test-modal]")
                if modals:
                    logger.info(f"检测到 {len(modals)} 个模态框")
                else:
                    logger.debug("未检测到模态框")
                
                # 检查是否有问题需要回答
                if self._answer_application_questions():
                    logger.info("回答了申请问题，继续下一步")
                    current_step += 1
                    continue
                
                # 使用智能按钮检测
                button, button_type = self._find_application_button()

                if button and button_type == 'submit':
                    logger.info("找到提交按钮，正在提交申请...")
                    button.click()
                    time.sleep(5)  # 增加等待时间

                    # 检查是否申请成功并处理成功通知
                    if self._check_and_handle_application_success():
                        logger.info("申请成功提交并已处理通知")
                        return True
                    else:
                        logger.info("未找到成功消息，但申请可能已提交")
                        return True

                elif button and button_type in ['next', 'review']:
                    button_text = button.text or button.get_attribute('aria-label') or f'{button_type} button'
                    logger.info(f"点击{button_type}按钮: {button_text}")

                    # 滚动到按钮位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                    time.sleep(1)

                    button.click()
                    current_step += 1
                    time.sleep(4)  # 增加等待时间

                else:
                    # 检查是否有错误或需要手动处理
                    logger.warning(f"申请流程步骤 {current_step + 1}: 未找到可点击的按钮")

                    # 尝试查找其他可能的按钮
                    all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    visible_buttons = [btn for btn in all_buttons if btn.is_displayed() and btn.is_enabled()]

                    if visible_buttons:
                        logger.info(f"找到 {len(visible_buttons)} 个可见按钮:")
                        for i, btn in enumerate(visible_buttons[:5]):  # 显示前5个
                            btn_text = btn.text.strip() or btn.get_attribute('aria-label') or f'Button {i+1}'
                            btn_class = btn.get_attribute('class') or 'no-class'
                            logger.info(f"  按钮 {i+1}: '{btn_text}' (class: {btn_class[:50]})")
                    else:
                        logger.warning("页面上没有找到任何可见的按钮")

                    # 保存当前页面截图用于调试
                    try:
                        self.driver.save_screenshot(f"log/application_stuck_step_{current_step + 1}_{int(time.time())}.png")
                        logger.info(f"已保存步骤 {current_step + 1} 的调试截图")
                    except Exception as e:
                        logger.warning(f"保存截图失败: {str(e)}")

                    break
            
            logger.warning("申请流程未能完成，可能需要手动处理")
            return False

        except Exception as e:
            logger.error(f"处理申请流程失败: {str(e)}")
            return False

    def _find_application_button(self) -> tuple:
        """智能查找申请流程中的按钮

        Returns:
            (button_element, button_type) - button_type可以是'next', 'submit', 'review'
        """
        try:
            # 定义按钮选择器和类型
            button_selectors = [
                # 提交按钮 - 优先级最高
                ("//button[@aria-label='Submit application']", 'submit'),
                ("//button[contains(@class, 'artdeco-button--primary') and contains(., 'Submit application')]", 'submit'),
                ("//button[contains(@class, 'artdeco-button--primary') and contains(., 'Submit')]", 'submit'),
                ("//button[contains(text(), 'Submit application')]", 'submit'),
                ("//button[contains(text(), 'Submit')]", 'submit'),

                # Review按钮
                ("//button[contains(@class, 'artdeco-button--primary') and contains(., 'Review')]", 'review'),
                ("//button[contains(text(), 'Review')]", 'review'),
                ("//button[contains(text(), '审核')]", 'review'),

                # Next按钮
                ("//button[@aria-label='Continue to next step']", 'next'),
                ("//button[contains(@class, 'artdeco-button--primary') and contains(., 'Next')]", 'next'),
                ("//button[contains(text(), 'Next')]", 'next'),
                ("//button[contains(text(), '下一步')]", 'next'),
            ]

            for selector, button_type in button_selectors:
                try:
                    button = self.driver.find_element(By.XPATH, selector)
                    if button.is_displayed() and button.is_enabled():
                        logger.info(f"找到{button_type}按钮: {button.text or button.get_attribute('aria-label')}")
                        return button, button_type
                except NoSuchElementException:
                    continue

            return None, None

        except Exception as e:
            logger.warning(f"查找申请按钮失败: {str(e)}")
            return None, None
    
    def _answer_application_questions(self) -> bool:
        """回答申请问题 - 增强版本"""
        try:
            answered = False
            logger.info("开始处理LinkedIn申请问题...")

            # 1. 处理文本输入框
            answered |= self._handle_text_inputs()

            # 2. 处理单选按钮组
            answered |= self._handle_radio_buttons()

            # 3. 处理下拉选择框
            answered |= self._handle_select_dropdowns()

            # 4. 处理复选框
            answered |= self._handle_checkboxes()

            # 5. 处理LinkedIn特殊的自定义下拉框
            answered |= self._handle_custom_dropdowns()

            # 6. 处理文件上传（如果需要）
            answered |= self._handle_file_uploads()

            if answered:
                logger.info("成功回答了申请问题")
                time.sleep(2)  # 等待页面响应
            else:
                logger.debug("未找到需要回答的问题")

            return answered

        except Exception as e:
            logger.warning(f"回答申请问题失败: {str(e)}")
            return False

    def _handle_text_inputs(self) -> bool:
        """处理文本输入框"""
        try:
            answered = False
            text_inputs = self.driver.find_elements(
                By.CSS_SELECTOR,
                "input[type='text'], input[type='number'], input[type='email'], input[type='tel'], textarea"
            )

            for input_field in text_inputs:
                if input_field.is_displayed() and input_field.is_enabled():
                    # 跳过已填写的字段
                    current_value = input_field.get_attribute('value') or ''
                    if current_value.strip():
                        continue

                    placeholder = input_field.get_attribute('placeholder') or ''
                    label = self._get_field_label(input_field)
                    question_text = f"{placeholder} {label}".strip()

                    answer = self._get_answer_for_question(question_text)
                    if answer:
                        logger.info(f"回答文本问题: {question_text[:50]}... -> {answer}")
                        input_field.clear()
                        self._human_type(input_field, answer)
                        answered = True
                        time.sleep(1)

            return answered

        except Exception as e:
            logger.warning(f"处理文本输入框失败: {str(e)}")
            return False

    def _handle_radio_buttons(self) -> bool:
        """处理单选按钮组"""
        try:
            answered = False

            # 查找单选按钮组
            radio_groups = self.driver.find_elements(
                By.CSS_SELECTOR,
                "fieldset, .fb-radio-buttons, [role='radiogroup']"
            )

            for group in radio_groups:
                if group.is_displayed():
                    radios = group.find_elements(By.CSS_SELECTOR, "input[type='radio']")
                    if radios and not any(radio.is_selected() for radio in radios):
                        # 获取问题文本
                        question_text = self._get_question_text_for_group(group)

                        # 智能选择答案
                        selected_radio = self._select_best_radio_option(radios, question_text)
                        if selected_radio:
                            logger.info(f"选择单选按钮: {question_text[:50]}...")
                            self.driver.execute_script("arguments[0].click();", selected_radio)
                            answered = True
                            time.sleep(1)

            return answered

        except Exception as e:
            logger.warning(f"处理单选按钮失败: {str(e)}")
            return False

    def _handle_select_dropdowns(self) -> bool:
        """处理标准下拉选择框"""
        try:
            answered = False
            selects = self.driver.find_elements(By.CSS_SELECTOR, "select")

            for select in selects:
                if select.is_displayed() and select.is_enabled():
                    # 检查是否已选择
                    selected_option = select.find_element(By.CSS_SELECTOR, "option:checked")
                    if selected_option and selected_option.get_attribute('value'):
                        continue

                    options = select.find_elements(By.CSS_SELECTOR, "option")
                    if len(options) > 1:
                        # 获取问题文本
                        label = self._get_field_label(select)
                        question_text = label or select.get_attribute('name') or ''

                        # 智能选择选项
                        best_option = self._select_best_dropdown_option(options, question_text)
                        if best_option:
                            logger.info(f"选择下拉选项: {question_text[:50]}... -> {best_option.text}")
                            best_option.click()
                            answered = True
                            time.sleep(1)

            return answered

        except Exception as e:
            logger.warning(f"处理下拉选择框失败: {str(e)}")
            return False

    def _handle_checkboxes(self) -> bool:
        """处理复选框"""
        try:
            answered = False
            checkboxes = self.driver.find_elements(By.CSS_SELECTOR, "input[type='checkbox']")

            for checkbox in checkboxes:
                if checkbox.is_displayed() and checkbox.is_enabled() and not checkbox.is_selected():
                    label = self._get_field_label(checkbox)
                    question_text = label or checkbox.get_attribute('name') or ''

                    # 根据问题内容决定是否勾选
                    should_check = self._should_check_checkbox(question_text)
                    if should_check:
                        logger.info(f"勾选复选框: {question_text[:50]}...")
                        checkbox.click()
                        answered = True
                        time.sleep(1)

            return answered

        except Exception as e:
            logger.warning(f"处理复选框失败: {str(e)}")
            return False

    def _handle_custom_dropdowns(self) -> bool:
        """处理LinkedIn自定义下拉框（如截图中的选择一项）"""
        try:
            answered = False

            # LinkedIn 2024年常见的自定义下拉框选择器
            custom_dropdown_selectors = [
                "button[aria-haspopup='listbox']",
                ".artdeco-dropdown__trigger",
                "[data-test-id*='dropdown']",
                "button[role='combobox']",
                ".fb-dropdown-trigger",
                "button:contains('选择一项')",
                "button:contains('Select an option')",
                "button:contains('Choose')"
            ]

            for selector in custom_dropdown_selectors:
                try:
                    dropdowns = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for dropdown in dropdowns:
                        if dropdown.is_displayed() and dropdown.is_enabled():
                            # 检查是否已选择（按钮文本不是默认值）
                            button_text = dropdown.text.strip()
                            if button_text in ['选择一项', 'Select an option', 'Choose', '']:
                                logger.info(f"点击自定义下拉框: {button_text}")
                                dropdown.click()
                                time.sleep(2)

                                # 查找并选择选项
                                if self._select_from_custom_dropdown():
                                    answered = True
                                    time.sleep(1)
                except Exception:
                    continue

            return answered

        except Exception as e:
            logger.warning(f"处理自定义下拉框失败: {str(e)}")
            return False

    def _handle_file_uploads(self) -> bool:
        """处理文件上传（简历等）"""
        try:
            answered = False
            file_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='file']")

            for file_input in file_inputs:
                if file_input.is_displayed():
                    # 这里可以上传默认简历文件
                    # 暂时跳过文件上传，避免复杂性
                    logger.info("检测到文件上传字段，暂时跳过")

            return answered

        except Exception as e:
            logger.warning(f"处理文件上传失败: {str(e)}")
            return False

    def _get_field_label(self, input_field) -> str:
        """获取输入框的标签"""
        try:
            # 尝试多种方式获取标签
            label_id = input_field.get_attribute('aria-labelledby')
            if label_id:
                label_element = self.driver.find_element(By.ID, label_id)
                return label_element.text
            
            # 查找相邻的label元素
            parent = input_field.find_element(By.XPATH, '..')
            label = parent.find_element(By.CSS_SELECTOR, 'label')
            return label.text
            
        except:
            return ''
    
    def _get_question_text_for_group(self, group_element) -> str:
        """获取问题组的文本"""
        try:
            # 尝试多种方式获取问题文本
            legend = group_element.find_element(By.CSS_SELECTOR, "legend")
            if legend:
                return legend.text
        except:
            pass

        try:
            label = group_element.find_element(By.CSS_SELECTOR, "label")
            if label:
                return label.text
        except:
            pass

        return group_element.text[:100] if group_element.text else ''

    def _select_best_radio_option(self, radios, question_text: str):
        """智能选择最佳单选选项"""
        try:
            question_lower = question_text.lower()

            # 获取所有选项的文本
            options_with_elements = []
            for radio in radios:
                try:
                    label = self._get_radio_label(radio)
                    options_with_elements.append((radio, label.lower()))
                except:
                    options_with_elements.append((radio, ''))

            # 根据问题类型智能选择
            if 'authorized' in question_lower or 'work authorization' in question_lower:
                # 工作授权问题 - 选择"是"
                for radio, label in options_with_elements:
                    if any(word in label for word in ['yes', '是', 'authorized', '有']):
                        return radio

            elif 'sponsorship' in question_lower or '赞助' in question_lower:
                # 赞助问题 - 选择"否"
                for radio, label in options_with_elements:
                    if any(word in label for word in ['no', '否', '不需要', 'not required']):
                        return radio

            elif 'relocate' in question_lower or '搬迁' in question_lower:
                # 搬迁问题 - 选择"是"
                for radio, label in options_with_elements:
                    if any(word in label for word in ['yes', '是', 'willing', '愿意']):
                        return radio

            # 默认选择第一个选项
            return radios[0] if radios else None

        except Exception as e:
            logger.warning(f"选择单选选项失败: {str(e)}")
            return radios[0] if radios else None

    def _get_radio_label(self, radio_element):
        """获取单选按钮的标签"""
        try:
            # 方法1: 通过for属性查找label
            radio_id = radio_element.get_attribute('id')
            if radio_id:
                label = self.driver.find_element(By.CSS_SELECTOR, f"label[for='{radio_id}']")
                return label.text
        except:
            pass

        try:
            # 方法2: 查找父元素中的label
            parent = radio_element.find_element(By.XPATH, '..')
            label = parent.find_element(By.CSS_SELECTOR, 'label')
            return label.text
        except:
            pass

        try:
            # 方法3: 查找相邻的文本
            return radio_element.find_element(By.XPATH, 'following-sibling::*').text
        except:
            pass

        return ''

    def _select_best_dropdown_option(self, options, question_text: str):
        """智能选择最佳下拉选项"""
        try:
            question_lower = question_text.lower()

            # 跳过空选项
            valid_options = [opt for opt in options if opt.get_attribute('value') and opt.text.strip()]

            if not valid_options:
                return None

            # 根据问题类型智能选择
            if 'experience' in question_lower or '经验' in question_lower:
                # 经验年数问题
                for option in valid_options:
                    if any(year in option.text for year in ['3', '2-3', '3-5']):
                        return option

            elif 'education' in question_lower or '学历' in question_lower:
                # 学历问题
                for option in valid_options:
                    if any(edu in option.text.lower() for edu in ['bachelor', 'degree', '学士', '本科']):
                        return option

            # 默认选择第一个有效选项
            return valid_options[0]

        except Exception as e:
            logger.warning(f"选择下拉选项失败: {str(e)}")
            return options[1] if len(options) > 1 else None

    def _should_check_checkbox(self, question_text: str) -> bool:
        """判断是否应该勾选复选框"""
        question_lower = question_text.lower()

        # 通常不勾选的情况
        negative_keywords = ['newsletter', 'marketing', 'promotional', '营销', '推广']
        if any(keyword in question_lower for keyword in negative_keywords):
            return False

        # 通常勾选的情况
        positive_keywords = ['terms', 'conditions', 'privacy', 'agree', '同意', '条款']
        if any(keyword in question_lower for keyword in positive_keywords):
            return True

        # 默认不勾选
        return False

    def _select_from_custom_dropdown(self) -> bool:
        """从自定义下拉框中选择选项"""
        try:
            time.sleep(1)  # 等待下拉框展开

            # LinkedIn常见的下拉选项选择器
            option_selectors = [
                "[role='option']",
                ".artdeco-dropdown__item",
                ".fb-dropdown-option",
                "li[data-test-id*='option']",
                ".dropdown-option",
                "ul li button",
                "ul li a"
            ]

            for selector in option_selectors:
                try:
                    options = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if options:
                        # 选择第一个可见的选项
                        for option in options:
                            if option.is_displayed() and option.is_enabled():
                                logger.info(f"选择自定义下拉选项: {option.text[:30]}...")
                                option.click()
                                return True
                except Exception:
                    continue

            return False

        except Exception as e:
            logger.warning(f"从自定义下拉框选择失败: {str(e)}")
            return False

    def _get_answer_for_question(self, question_text: str) -> str:
        """根据问题文本获取答案 - 增强版本，支持用户设置文件"""
        question_lower = question_text.lower()

        # 获取默认答案配置，支持新旧配置格式
        if 'application' in self.config.get('linkedin', {}):
            # 新格式：linkedin.application.default_answers
            default_answers = self.config['linkedin']['application'].get('default_answers', {})
        else:
            # 旧格式：linkedin.default_answers
            default_answers = self.config.get('linkedin', {}).get('default_answers', {})

        # 经验年数问题
        if any(keyword in question_lower for keyword in ['experience', '经验', 'years', '年']):
            return default_answers.get('years_experience', '15')

        # 搬迁问题
        elif any(keyword in question_lower for keyword in ['relocate', '搬迁', 'move', '迁移']):
            return default_answers.get('willing_to_relocate', 'Yes')

        # 工作授权问题
        elif any(keyword in question_lower for keyword in ['authorized', 'work authorization', '工作授权', 'eligible']):
            return default_answers.get('authorized_to_work', 'Yes')

        # 赞助问题
        elif any(keyword in question_lower for keyword in ['sponsorship', '赞助', 'visa', '签证']):
            return default_answers.get('require_sponsorship', 'No')

        # 联系方式
        elif any(keyword in question_lower for keyword in ['phone', '电话', 'mobile', '手机']):
            return default_answers.get('phone', '1234567890')
        elif any(keyword in question_lower for keyword in ['email', '邮箱', '@']):
            return default_answers.get('email', '<EMAIL>')

        # 薪资期望
        elif any(keyword in question_lower for keyword in ['salary', '薪资', 'compensation', '薪酬', 'expected']):
            return default_answers.get('expected_salary', 'Negotiable面议')

        # 开始时间
        elif any(keyword in question_lower for keyword in ['start', '开始', 'available', '可入职', 'when']):
            return default_answers.get('start_date', 'Immediately立即')

        return ''

    def _check_and_handle_application_success(self) -> bool:
        """检查申请是否成功并处理成功通知窗口"""
        try:
            # 等待成功消息出现
            time.sleep(3)

            # 多种成功消息的选择器
            success_selectors = [
                "//*[contains(text(), 'Application sent')]",
                "//*[contains(text(), 'Your application was sent')]",
                "//*[contains(text(), 'Application submitted')]",
                "//*[contains(text(), '申请已发送')]",
                "//*[contains(text(), '申请已提交')]",
                "//*[contains(text(), 'Successfully applied')]",
                "//h2[contains(text(), 'Application sent')]",
                "//div[contains(@class, 'success') and contains(text(), 'sent')]"
            ]

            success_found = False
            for selector in success_selectors:
                try:
                    success_element = self.driver.find_element(By.XPATH, selector)
                    if success_element.is_displayed():
                        logger.info(f"检测到申请成功消息: {success_element.text[:50]}...")
                        success_found = True
                        break
                except NoSuchElementException:
                    continue

            if success_found:
                # 处理成功通知弹窗
                self._close_success_notification()
                return True

            # 如果没有找到明确的成功消息，检查是否有其他成功指示器
            return self._check_alternative_success_indicators()

        except Exception as e:
            logger.warning(f"检查申请成功状态失败: {str(e)}")
            return False

    def _close_success_notification(self):
        """关闭申请成功通知窗口"""
        try:
            logger.info("尝试关闭申请成功通知窗口...")

            # 等待通知窗口完全加载
            time.sleep(2)

            # 多种关闭按钮的选择器
            close_selectors = [
                # 标准关闭按钮
                "button[aria-label='Close']",
                "button[aria-label='关闭']",
                "button[data-test-id='close-button']",
                ".artdeco-modal__dismiss",
                ".artdeco-toast-item__dismiss",

                # X 按钮
                "button:contains('×')",
                "button:contains('✕')",
                "[aria-label*='close' i]",

                # 确定/OK按钮
                "button:contains('OK')",
                "button:contains('确定')",
                "button:contains('Got it')",
                "button:contains('知道了')",

                # 继续按钮
                "button:contains('Continue')",
                "button:contains('继续')",
                "button:contains('Done')",
                "button:contains('完成')",

                # LinkedIn特定的关闭按钮
                ".jobs-apply-success-modal__dismiss",
                ".application-success-modal .artdeco-button",
                "[data-test-id*='success-modal'] button"
            ]

            # 尝试点击关闭按钮
            for selector in close_selectors:
                try:
                    close_buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for button in close_buttons:
                        if button.is_displayed() and button.is_enabled():
                            logger.info(f"点击关闭按钮: {selector}")
                            button.click()
                            time.sleep(2)
                            return True
                except Exception:
                    continue

            # 如果没有找到关闭按钮，尝试按ESC键
            try:
                logger.info("尝试按ESC键关闭通知")
                from selenium.webdriver.common.keys import Keys
                self.driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
                time.sleep(2)
            except Exception:
                pass

            # 最后尝试点击模态框外部区域
            try:
                logger.info("尝试点击模态框外部区域")
                overlay = self.driver.find_element(By.CSS_SELECTOR, ".artdeco-modal__overlay")
                if overlay.is_displayed():
                    overlay.click()
                    time.sleep(2)
            except Exception:
                pass

            logger.info("成功通知窗口处理完成")
            return True

        except Exception as e:
            logger.warning(f"关闭成功通知窗口失败: {str(e)}")
            return False

    def _check_alternative_success_indicators(self) -> bool:
        """检查其他成功指示器"""
        try:
            # 检查URL变化（通常申请成功后会跳转）
            current_url = self.driver.current_url
            if 'applied' in current_url or 'success' in current_url:
                logger.info("通过URL变化检测到申请成功")
                return True

            # 检查页面标题变化
            page_title = self.driver.title
            if any(keyword in page_title.lower() for keyword in ['applied', 'success', 'sent']):
                logger.info("通过页面标题检测到申请成功")
                return True

            # 检查是否出现了"查看申请"或类似按钮
            view_application_selectors = [
                "//button[contains(text(), 'View application')]",
                "//button[contains(text(), '查看申请')]",
                "//a[contains(text(), 'View application')]",
                "//a[contains(text(), '查看申请')]"
            ]

            for selector in view_application_selectors:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    if element.is_displayed():
                        logger.info("通过'查看申请'按钮检测到申请成功")
                        return True
                except NoSuchElementException:
                    continue

            return False

        except Exception as e:
            logger.warning(f"检查其他成功指示器失败: {str(e)}")
            return False

    def _human_type(self, element, text: str):
        """模拟人类打字"""
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.15))
    
    def batch_apply_jobs(self, max_applications: int = None) -> Dict:
        """批量申请职位"""
        if not max_applications:
            max_applications = self.config['linkedin']['max_applications_per_day']
        
        results = {
            'total_found': 0,
            'total_applied': 0,
            'successful_applications': [],
            'failed_applications': []
        }
        
        try:
            # 搜索职位
            for keyword in self.config['linkedin']['search_keywords']:
                if results['total_applied'] >= max_applications:
                    break
                    
                jobs = self.search_jobs(keyword)
                results['total_found'] += len(jobs)
                
                for job in jobs:
                    if results['total_applied'] >= max_applications:
                        break
                    
                    if self.apply_to_job(job):
                        results['total_applied'] += 1
                        results['successful_applications'].append(job)
                    else:
                        results['failed_applications'].append(job)
            
            logger.info(f"批量申请完成: 找到 {results['total_found']} 个职位，成功申请 {results['total_applied']} 个")
            return results
            
        except Exception as e:
            logger.error(f"批量申请失败: {str(e)}")
            return results
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            try:
                # 如果使用了Undetected ChromeDriver管理器，使用其quit方法
                if hasattr(self, '_chrome_manager') and self._chrome_manager:
                    self._chrome_manager.quit()
                else:
                    self.driver.quit()
                logger.info("浏览器已关闭")
            except Exception as e:
                logger.warning(f"关闭浏览器时出错: {str(e)}")
            finally:
                self.driver = None
                self.wait = None
                self.is_logged_in = False
                if hasattr(self, '_chrome_manager'):
                    self._chrome_manager = None
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


# 使用示例
if __name__ == "__main__":
    # 创建配置文件示例
    config = {
        'linkedin': {
            'email': '<EMAIL>',
            'password': 'your_password',
            'search_keywords': ['python developer', 'software engineer', 'data scientist'],
            'location': 'United States',
            'max_applications_per_day': 20,
            'delay_between_applications': [30, 60],
            'auto_answer_questions': True,
            'default_answers': {
                'years_experience': '3',
                'willing_to_relocate': 'Yes',
                'authorized_to_work': 'Yes',
                'require_sponsorship': 'No'
            }
        },
        'selenium': {
            'headless': False,
            'implicit_wait': 10,
            'page_load_timeout': 30
        }
    }
    
    # 保存配置文件
    with open('linkedin_config.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    # 使用自动化工具
    with LinkedInAutomation('linkedin_config.yaml') as linkedin:
        linkedin.setup_driver()
        
        if linkedin.login():
            results = linkedin.batch_apply_jobs(max_applications=10)
            print(f"申请结果: {results}")